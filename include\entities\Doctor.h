#pragma once

#include <string>
#include <vector>
#include <map>
#include <nlohmann/json.hpp>
#include "core/Utils.h"

namespace HospitalSystem {
namespace Entities {

/**
 * @brief Doctor entity class representing a hospital doctor
 * 
 * This class encapsulates all doctor-related information including
 * personal details, specialization, qualifications, and schedule.
 */
class Doctor {
public:
    /**
     * @brief Enumeration for days of the week
     */
    enum class WeekDay {
        MONDAY = 0,
        TUESDAY = 1,
        WEDNESDAY = 2,
        THURSDAY = 3,
        FRIDAY = 4,
        SATURDAY = 5,
        SUNDAY = 6
    };

    /**
     * @brief Structure representing a time slot
     */
    struct TimeSlot {
        int start_hour;
        int start_minute;
        int end_hour;
        int end_minute;

        TimeSlot() : start_hour(0), start_minute(0), end_hour(0), end_minute(0) {}
        TimeSlot(int sh, int sm, int eh, int em) 
            : start_hour(sh), start_minute(sm), end_hour(eh), end_minute(em) {}

        std::string to_string() const;
        bool is_valid() const;
        bool contains_time(int hour, int minute) const;
    };

    /**
     * @brief Default constructor
     */
    Doctor();

    /**
     * @brief Constructor with basic doctor information
     * @param doctor_id Unique doctor identifier
     * @param name Doctor's full name
     * @param specialization Doctor's medical specialization
     */
    Doctor(const std::string& doctor_id, const std::string& name, 
           const std::string& specialization);

    /**
     * @brief Constructor with complete doctor information
     * @param doctor_id Unique doctor identifier
     * @param name Doctor's full name
     * @param specialization Doctor's medical specialization
     * @param phone Doctor's phone number
     * @param email Doctor's email address
     */
    Doctor(const std::string& doctor_id, const std::string& name, 
           const std::string& specialization, const std::string& phone,
           const std::string& email);

    // Getters
    int get_id() const { return id_; }
    const std::string& get_doctor_id() const { return doctor_id_; }
    const std::string& get_name() const { return name_; }
    const std::string& get_specialization() const { return specialization_; }
    const std::string& get_phone() const { return phone_; }
    const std::string& get_email() const { return email_; }
    const std::vector<std::string>& get_qualifications() const { return qualifications_; }
    const std::map<WeekDay, TimeSlot>& get_schedule() const { return schedule_; }
    const Core::DateTime& get_created_at() const { return created_at_; }
    const Core::DateTime& get_updated_at() const { return updated_at_; }

    // Setters
    void set_id(int id) { id_ = id; }
    void set_doctor_id(const std::string& doctor_id) { doctor_id_ = doctor_id; }
    void set_name(const std::string& name);
    void set_specialization(const std::string& specialization);
    void set_phone(const std::string& phone);
    void set_email(const std::string& email);
    void set_qualifications(const std::vector<std::string>& qualifications);
    void set_created_at(const Core::DateTime& created_at) { created_at_ = created_at; }
    void set_updated_at(const Core::DateTime& updated_at) { updated_at_ = updated_at; }

    /**
     * @brief Add a qualification to the doctor's qualification list
     * @param qualification Qualification to add
     */
    void add_qualification(const std::string& qualification);

    /**
     * @brief Remove a qualification from the doctor's qualification list
     * @param qualification Qualification to remove
     * @return True if qualification was found and removed, false otherwise
     */
    bool remove_qualification(const std::string& qualification);

    /**
     * @brief Check if doctor has a specific qualification
     * @param qualification Qualification to check for
     * @return True if doctor has the qualification, false otherwise
     */
    bool has_qualification(const std::string& qualification) const;

    /**
     * @brief Set availability for a specific day
     * @param day Day of the week
     * @param time_slot Time slot for availability
     */
    void set_availability(WeekDay day, const TimeSlot& time_slot);

    /**
     * @brief Remove availability for a specific day
     * @param day Day of the week
     */
    void remove_availability(WeekDay day);

    /**
     * @brief Check if doctor is available at a specific date and time
     * @param date_time DateTime to check availability for
     * @return True if doctor is available, false otherwise
     */
    bool is_available(const Core::DateTime& date_time) const;

    /**
     * @brief Get available time slots for a specific day
     * @param day Day of the week
     * @return TimeSlot if available, empty TimeSlot if not
     */
    TimeSlot get_availability(WeekDay day) const;

    /**
     * @brief Validate doctor data
     * @return True if all doctor data is valid, false otherwise
     */
    bool is_valid() const;

    /**
     * @brief Get validation errors
     * @return Vector of validation error messages
     */
    std::vector<std::string> get_validation_errors() const;

    /**
     * @brief Convert doctor to JSON representation
     * @return JSON object representing the doctor
     */
    nlohmann::json to_json() const;

    /**
     * @brief Create doctor from JSON representation
     * @param json JSON object containing doctor data
     * @return Doctor object created from JSON
     * @throws std::invalid_argument if JSON is invalid
     */
    static Doctor from_json(const nlohmann::json& json);

    /**
     * @brief Get doctor's full information as formatted string
     * @return Formatted string with doctor information
     */
    std::string to_string() const;

    /**
     * @brief Update the updated_at timestamp to current time
     */
    void touch();

    /**
     * @brief Convert WeekDay enum to string
     * @param day WeekDay enum value
     * @return String representation of the day
     */
    static std::string weekday_to_string(WeekDay day);

    /**
     * @brief Convert string to WeekDay enum
     * @param day_str String representation of the day
     * @return WeekDay enum value
     * @throws std::invalid_argument if string is invalid
     */
    static WeekDay string_to_weekday(const std::string& day_str);

    // Comparison operators
    bool operator==(const Doctor& other) const;
    bool operator!=(const Doctor& other) const;

private:
    int id_;                                        // Database ID (auto-increment)
    std::string doctor_id_;                         // Unique doctor identifier
    std::string name_;                              // Doctor's full name
    std::string specialization_;                    // Doctor's medical specialization
    std::string phone_;                             // Doctor's phone number
    std::string email_;                             // Doctor's email address
    std::vector<std::string> qualifications_;       // List of doctor's qualifications
    std::map<WeekDay, TimeSlot> schedule_;          // Doctor's weekly schedule
    Core::DateTime created_at_;                     // Creation timestamp
    Core::DateTime updated_at_;                     // Last update timestamp

    /**
     * @brief Initialize default values
     */
    void initialize_defaults();

    /**
     * @brief Get day of week from DateTime
     * @param date_time DateTime object
     * @return WeekDay enum value
     */
    WeekDay get_weekday_from_datetime(const Core::DateTime& date_time) const;
};

} // namespace Entities
} // namespace HospitalSystem
