#include "entities/Appointment.h"
#include "core/Utils.h"
#include <sstream>
#include <chrono>

namespace HospitalSystem {
namespace Entities {

Appointment::Appointment() {
    initialize_defaults();
}

Appointment::Appointment(const std::string& appointment_id, const std::string& patient_id,
                        const std::string& doctor_id, const Core::DateTime& appointment_datetime)
    : appointment_id_(appointment_id), patient_id_(patient_id), doctor_id_(doctor_id),
      appointment_datetime_(appointment_datetime) {
    initialize_defaults();
}

Appointment::Appointment(const std::string& appointment_id, const std::string& patient_id,
                        const std::string& doctor_id, const Core::DateTime& appointment_datetime,
                        Status status, const std::string& notes)
    : appointment_id_(appointment_id), patient_id_(patient_id), doctor_id_(doctor_id),
      appointment_datetime_(appointment_datetime), status_(status), notes_(notes) {
    initialize_defaults();
}

void Appointment::initialize_defaults() {
    id_ = 0;
    if (status_ != Status::SCHEDULED && status_ != Status::CONFIRMED && 
        status_ != Status::IN_PROGRESS && status_ != Status::COMPLETED && 
        status_ != Status::CANCELLED && status_ != Status::NO_SHOW) {
        status_ = Status::SCHEDULED;
    }
    created_at_ = Core::DateTime::now();
    updated_at_ = created_at_;
}

void Appointment::set_patient_id(const std::string& patient_id) {
    if (!patient_id.empty()) {
        patient_id_ = patient_id;
        touch();
    }
}

void Appointment::set_doctor_id(const std::string& doctor_id) {
    if (!doctor_id.empty()) {
        doctor_id_ = doctor_id;
        touch();
    }
}

void Appointment::set_appointment_datetime(const Core::DateTime& appointment_datetime) {
    appointment_datetime_ = appointment_datetime;
    touch();
}

void Appointment::set_status(Status status) {
    status_ = status;
    touch();
}

void Appointment::set_notes(const std::string& notes) {
    notes_ = notes;
    touch();
}

bool Appointment::can_be_cancelled() const {
    return status_ == Status::SCHEDULED || status_ == Status::CONFIRMED;
}

bool Appointment::can_be_rescheduled() const {
    return status_ == Status::SCHEDULED || status_ == Status::CONFIRMED;
}

bool Appointment::is_past_due() const {
    return appointment_datetime_.is_before(Core::DateTime::now());
}

bool Appointment::is_today() const {
    Core::DateTime now = Core::DateTime::now();
    return appointment_datetime_.to_date_string() == now.to_date_string();
}

int Appointment::minutes_until_appointment() const {
    Core::DateTime now = Core::DateTime::now();
    
    // Calculate difference in minutes
    auto duration = std::chrono::duration_cast<std::chrono::minutes>(
        std::chrono::system_clock::time_point() - std::chrono::system_clock::time_point()
    );
    
    // Simple approximation - in a real implementation, you'd use proper time calculation
    int days_diff = appointment_datetime_.days_difference(now);
    return days_diff * 24 * 60; // Convert days to minutes (approximation)
}

bool Appointment::cancel(const std::string& reason) {
    if (!can_be_cancelled()) {
        return false;
    }
    
    status_ = Status::CANCELLED;
    if (!reason.empty()) {
        if (!notes_.empty()) {
            notes_ += "\nCancellation reason: " + reason;
        } else {
            notes_ = "Cancellation reason: " + reason;
        }
    }
    touch();
    return true;
}

bool Appointment::confirm() {
    if (status_ != Status::SCHEDULED) {
        return false;
    }
    
    status_ = Status::CONFIRMED;
    touch();
    return true;
}

bool Appointment::start() {
    if (status_ != Status::CONFIRMED && status_ != Status::SCHEDULED) {
        return false;
    }
    
    status_ = Status::IN_PROGRESS;
    touch();
    return true;
}

bool Appointment::complete(const std::string& completion_notes) {
    if (status_ != Status::IN_PROGRESS) {
        return false;
    }
    
    status_ = Status::COMPLETED;
    if (!completion_notes.empty()) {
        if (!notes_.empty()) {
            notes_ += "\nCompletion notes: " + completion_notes;
        } else {
            notes_ = "Completion notes: " + completion_notes;
        }
    }
    touch();
    return true;
}

bool Appointment::mark_no_show() {
    if (status_ != Status::SCHEDULED && status_ != Status::CONFIRMED) {
        return false;
    }
    
    status_ = Status::NO_SHOW;
    touch();
    return true;
}

bool Appointment::reschedule(const Core::DateTime& new_datetime) {
    if (!can_be_rescheduled()) {
        return false;
    }
    
    if (!Core::ValidationUtils::is_valid_appointment_time(new_datetime)) {
        return false;
    }
    
    appointment_datetime_ = new_datetime;
    status_ = Status::SCHEDULED; // Reset to scheduled after rescheduling
    touch();
    return true;
}

bool Appointment::is_valid() const {
    return get_validation_errors().empty();
}

std::vector<std::string> Appointment::get_validation_errors() const {
    std::vector<std::string> errors;

    if (appointment_id_.empty()) {
        errors.push_back("Appointment ID cannot be empty");
    }

    if (patient_id_.empty()) {
        errors.push_back("Patient ID cannot be empty");
    }

    if (doctor_id_.empty()) {
        errors.push_back("Doctor ID cannot be empty");
    }

    if (!Core::ValidationUtils::is_valid_appointment_time(appointment_datetime_)) {
        errors.push_back("Invalid appointment time (must be in future and during working hours)");
    }

    return errors;
}

nlohmann::json Appointment::to_json() const {
    nlohmann::json j;
    j["id"] = id_;
    j["appointment_id"] = appointment_id_;
    j["patient_id"] = patient_id_;
    j["doctor_id"] = doctor_id_;
    j["appointment_datetime"] = appointment_datetime_.to_string();
    j["status"] = status_to_string(status_);
    j["notes"] = notes_;
    j["created_at"] = created_at_.to_string();
    j["updated_at"] = updated_at_.to_string();
    return j;
}

Appointment Appointment::from_json(const nlohmann::json& json) {
    Appointment appointment;
    
    if (json.contains("id")) {
        appointment.set_id(json["id"]);
    }
    
    if (json.contains("appointment_id")) {
        appointment.set_appointment_id(json["appointment_id"]);
    }
    
    if (json.contains("patient_id")) {
        appointment.set_patient_id(json["patient_id"]);
    }
    
    if (json.contains("doctor_id")) {
        appointment.set_doctor_id(json["doctor_id"]);
    }
    
    if (json.contains("appointment_datetime")) {
        appointment.set_appointment_datetime(Core::DateTime(json["appointment_datetime"]));
    }
    
    if (json.contains("status")) {
        appointment.set_status(string_to_status(json["status"]));
    }
    
    if (json.contains("notes")) {
        appointment.set_notes(json["notes"]);
    }
    
    if (json.contains("created_at")) {
        appointment.set_created_at(Core::DateTime(json["created_at"]));
    }
    
    if (json.contains("updated_at")) {
        appointment.set_updated_at(Core::DateTime(json["updated_at"]));
    }
    
    return appointment;
}

std::string Appointment::to_string() const {
    std::ostringstream oss;
    oss << "Appointment Information:\n";
    oss << "  ID: " << appointment_id_ << "\n";
    oss << "  Patient ID: " << patient_id_ << "\n";
    oss << "  Doctor ID: " << doctor_id_ << "\n";
    oss << "  Date & Time: " << appointment_datetime_.to_string() << "\n";
    oss << "  Status: " << status_to_string(status_) << "\n";
    
    if (!notes_.empty()) {
        oss << "  Notes: " << notes_ << "\n";
    }
    
    oss << "  Created: " << created_at_.to_string() << "\n";
    oss << "  Updated: " << updated_at_.to_string();
    
    return oss.str();
}

void Appointment::touch() {
    updated_at_ = Core::DateTime::now();
}

std::string Appointment::status_to_string(Status status) {
    switch (status) {
        case Status::SCHEDULED:   return "SCHEDULED";
        case Status::CONFIRMED:   return "CONFIRMED";
        case Status::IN_PROGRESS: return "IN_PROGRESS";
        case Status::COMPLETED:   return "COMPLETED";
        case Status::CANCELLED:   return "CANCELLED";
        case Status::NO_SHOW:     return "NO_SHOW";
        default:                  return "UNKNOWN";
    }
}

Appointment::Status Appointment::string_to_status(const std::string& status_str) {
    std::string upper_status = Core::StringUtils::to_upper(status_str);
    
    if (upper_status == "SCHEDULED") return Status::SCHEDULED;
    if (upper_status == "CONFIRMED") return Status::CONFIRMED;
    if (upper_status == "IN_PROGRESS") return Status::IN_PROGRESS;
    if (upper_status == "COMPLETED") return Status::COMPLETED;
    if (upper_status == "CANCELLED") return Status::CANCELLED;
    if (upper_status == "NO_SHOW") return Status::NO_SHOW;
    
    throw std::invalid_argument("Invalid appointment status string: " + status_str);
}

bool Appointment::operator==(const Appointment& other) const {
    return appointment_id_ == other.appointment_id_;
}

bool Appointment::operator!=(const Appointment& other) const {
    return !(*this == other);
}

bool Appointment::operator<(const Appointment& other) const {
    return appointment_datetime_ < other.appointment_datetime_;
}

} // namespace Entities
} // namespace HospitalSystem
