#include "entities/Patient.h"
#include "core/Utils.h"
#include <algorithm>
#include <sstream>

namespace HospitalSystem {
namespace Entities {

Patient::Patient() {
    initialize_defaults();
}

Patient::Patient(const std::string& patient_id, const std::string& name, 
                 int age, const std::string& gender) 
    : patient_id_(patient_id), name_(name), age_(age), gender_(gender) {
    initialize_defaults();
}

Patient::Patient(const std::string& patient_id, const std::string& name, 
                 int age, const std::string& gender, const std::string& phone,
                 const std::string& email, const std::string& address)
    : patient_id_(patient_id), name_(name), age_(age), gender_(gender),
      phone_(phone), email_(email), address_(address) {
    initialize_defaults();
}

void Patient::initialize_defaults() {
    id_ = 0;
    created_at_ = Core::DateTime::now();
    updated_at_ = created_at_;
}

void Patient::set_name(const std::string& name) {
    if (Core::ValidationUtils::is_valid_name(name)) {
        name_ = name;
        touch();
    }
}

void Patient::set_age(int age) {
    if (Core::ValidationUtils::is_valid_age(age)) {
        age_ = age;
        touch();
    }
}

void Patient::set_gender(const std::string& gender) {
    if (Core::ValidationUtils::is_valid_gender(gender)) {
        gender_ = gender;
        touch();
    }
}

void Patient::set_phone(const std::string& phone) {
    if (phone.empty() || Core::StringUtils::is_valid_phone(phone)) {
        phone_ = phone;
        touch();
    }
}

void Patient::set_email(const std::string& email) {
    if (email.empty() || Core::StringUtils::is_valid_email(email)) {
        email_ = email;
        touch();
    }
}

void Patient::set_address(const std::string& address) {
    address_ = address;
    touch();
}

void Patient::set_medical_history(const std::string& medical_history) {
    medical_history_ = medical_history;
    touch();
}

void Patient::set_allergies(const std::vector<std::string>& allergies) {
    allergies_ = allergies;
    touch();
}

void Patient::add_allergy(const std::string& allergy) {
    if (!allergy.empty() && !has_allergy(allergy)) {
        allergies_.push_back(allergy);
        touch();
    }
}

bool Patient::remove_allergy(const std::string& allergy) {
    auto it = std::find(allergies_.begin(), allergies_.end(), allergy);
    if (it != allergies_.end()) {
        allergies_.erase(it);
        touch();
        return true;
    }
    return false;
}

bool Patient::has_allergy(const std::string& allergy) const {
    return std::find(allergies_.begin(), allergies_.end(), allergy) != allergies_.end();
}

void Patient::update_medical_history(const std::string& new_history) {
    if (!new_history.empty()) {
        if (!medical_history_.empty()) {
            medical_history_ += "\n" + Core::DateTime::now().to_string() + ": " + new_history;
        } else {
            medical_history_ = Core::DateTime::now().to_string() + ": " + new_history;
        }
        touch();
    }
}

bool Patient::is_valid() const {
    return get_validation_errors().empty();
}

std::vector<std::string> Patient::get_validation_errors() const {
    std::vector<std::string> errors;

    if (patient_id_.empty()) {
        errors.push_back("Patient ID cannot be empty");
    }

    if (!Core::ValidationUtils::is_valid_name(name_)) {
        errors.push_back("Invalid patient name");
    }

    if (!Core::ValidationUtils::is_valid_age(age_)) {
        errors.push_back("Invalid age (must be between 0 and 150)");
    }

    if (!Core::ValidationUtils::is_valid_gender(gender_)) {
        errors.push_back("Invalid gender (must be Male, Female, or Other)");
    }

    if (!phone_.empty() && !Core::StringUtils::is_valid_phone(phone_)) {
        errors.push_back("Invalid phone number format");
    }

    if (!email_.empty() && !Core::StringUtils::is_valid_email(email_)) {
        errors.push_back("Invalid email format");
    }

    return errors;
}

nlohmann::json Patient::to_json() const {
    nlohmann::json j;
    j["id"] = id_;
    j["patient_id"] = patient_id_;
    j["name"] = name_;
    j["age"] = age_;
    j["gender"] = gender_;
    j["phone"] = phone_;
    j["email"] = email_;
    j["address"] = address_;
    j["medical_history"] = medical_history_;
    j["allergies"] = allergies_;
    j["created_at"] = created_at_.to_string();
    j["updated_at"] = updated_at_.to_string();
    return j;
}

Patient Patient::from_json(const nlohmann::json& json) {
    Patient patient;
    
    if (json.contains("id")) {
        patient.set_id(json["id"]);
    }
    
    if (json.contains("patient_id")) {
        patient.set_patient_id(json["patient_id"]);
    }
    
    if (json.contains("name")) {
        patient.set_name(json["name"]);
    }
    
    if (json.contains("age")) {
        patient.set_age(json["age"]);
    }
    
    if (json.contains("gender")) {
        patient.set_gender(json["gender"]);
    }
    
    if (json.contains("phone")) {
        patient.set_phone(json["phone"]);
    }
    
    if (json.contains("email")) {
        patient.set_email(json["email"]);
    }
    
    if (json.contains("address")) {
        patient.set_address(json["address"]);
    }
    
    if (json.contains("medical_history")) {
        patient.set_medical_history(json["medical_history"]);
    }
    
    if (json.contains("allergies")) {
        patient.set_allergies(json["allergies"]);
    }
    
    if (json.contains("created_at")) {
        patient.set_created_at(Core::DateTime(json["created_at"]));
    }
    
    if (json.contains("updated_at")) {
        patient.set_updated_at(Core::DateTime(json["updated_at"]));
    }
    
    return patient;
}

std::string Patient::to_string() const {
    std::ostringstream oss;
    oss << "Patient Information:\n";
    oss << "  ID: " << patient_id_ << "\n";
    oss << "  Name: " << name_ << "\n";
    oss << "  Age: " << age_ << "\n";
    oss << "  Gender: " << gender_ << "\n";
    
    if (!phone_.empty()) {
        oss << "  Phone: " << phone_ << "\n";
    }
    
    if (!email_.empty()) {
        oss << "  Email: " << email_ << "\n";
    }
    
    if (!address_.empty()) {
        oss << "  Address: " << address_ << "\n";
    }
    
    if (!allergies_.empty()) {
        oss << "  Allergies: " << Core::StringUtils::join(allergies_, ", ") << "\n";
    }
    
    if (!medical_history_.empty()) {
        oss << "  Medical History: " << medical_history_ << "\n";
    }
    
    oss << "  Created: " << created_at_.to_string() << "\n";
    oss << "  Updated: " << updated_at_.to_string();
    
    return oss.str();
}

void Patient::touch() {
    updated_at_ = Core::DateTime::now();
}

bool Patient::operator==(const Patient& other) const {
    return patient_id_ == other.patient_id_;
}

bool Patient::operator!=(const Patient& other) const {
    return !(*this == other);
}

} // namespace Entities
} // namespace HospitalSystem
