#pragma once

#include <string>
#include <vector>
#include <memory>
#include <optional>
#include "entities/Patient.h"
#include "entities/Doctor.h"
#include "entities/Medicine.h"
#include "entities/Appointment.h"
#include "core/Database.h"
#include "core/Logger.h"
#include "core/Utils.h"

namespace HospitalSystem {
namespace Services {

/**
 * @brief Service class for managing billing and financial operations
 * 
 * This class provides billing operations including invoice generation,
 * payment processing, and financial reporting.
 */
class BillingService {
public:
    /**
     * @brief Structure representing a bill item
     */
    struct BillItem {
        std::string item_id;
        std::string description;
        int quantity;
        double unit_price;
        double total_price;
        
        BillItem(const std::string& id = "", const std::string& desc = "", 
                int qty = 0, double price = 0.0)
            : item_id(id), description(desc), quantity(qty), unit_price(price),
              total_price(qty * price) {}
    };

    /**
     * @brief Structure representing a complete bill
     */
    struct Bill {
        int id;
        std::string bill_id;
        std::string patient_id;
        std::string doctor_id;
        std::vector<BillItem> items;
        double subtotal;
        double tax_amount;
        double discount_amount;
        double total_amount;
        bool is_paid;
        Core::DateTime created_at;
        Core::DateTime updated_at;
        Core::DateTime paid_at;
        std::string payment_method;
        std::string notes;
        
        Bill() : id(0), subtotal(0.0), tax_amount(0.0), discount_amount(0.0),
                total_amount(0.0), is_paid(false) {}
    };

    /**
     * @brief Result structure for service operations
     */
    struct ServiceResult {
        bool success;
        std::string message;
        int affected_rows;
        
        ServiceResult(bool s = false, const std::string& m = "", int rows = 0)
            : success(s), message(m), affected_rows(rows) {}
    };

    /**
     * @brief Constructor
     * @param database Reference to database instance
     * @param logger Reference to logger instance
     */
    BillingService(Core::Database& database, Core::Logger& logger);

    /**
     * @brief Destructor
     */
    ~BillingService() = default;

    // Disable copy constructor and assignment operator
    BillingService(const BillingService&) = delete;
    BillingService& operator=(const BillingService&) = delete;

    /**
     * @brief Create a new bill
     * @param patient_id Patient's unique identifier
     * @param doctor_id Doctor's unique identifier
     * @param items Vector of bill items
     * @return ServiceResult with operation status and generated bill ID
     */
    ServiceResult create_bill(const std::string& patient_id, const std::string& doctor_id,
                             const std::vector<BillItem>& items);

    /**
     * @brief Get bill by ID
     * @param bill_id Bill's unique identifier
     * @return Optional Bill object (empty if not found)
     */
    std::optional<Bill> get_bill(const std::string& bill_id);

    /**
     * @brief Get bill by database ID
     * @param id Database ID
     * @return Optional Bill object (empty if not found)
     */
    std::optional<Bill> get_bill_by_id(int id);

    /**
     * @brief Update existing bill
     * @param bill Bill object with updated information
     * @return ServiceResult with operation status
     */
    ServiceResult update_bill(const Bill& bill);

    /**
     * @brief Delete bill by ID
     * @param bill_id Bill's unique identifier
     * @return ServiceResult with operation status
     */
    ServiceResult delete_bill(const std::string& bill_id);

    /**
     * @brief Add item to existing bill
     * @param bill_id Bill's unique identifier
     * @param item Bill item to add
     * @return ServiceResult with operation status
     */
    ServiceResult add_bill_item(const std::string& bill_id, const BillItem& item);

    /**
     * @brief Remove item from bill
     * @param bill_id Bill's unique identifier
     * @param item_id Item's unique identifier
     * @return ServiceResult with operation status
     */
    ServiceResult remove_bill_item(const std::string& bill_id, const std::string& item_id);

    /**
     * @brief Apply discount to bill
     * @param bill_id Bill's unique identifier
     * @param discount_amount Discount amount to apply
     * @param discount_reason Reason for discount
     * @return ServiceResult with operation status
     */
    ServiceResult apply_discount(const std::string& bill_id, double discount_amount,
                                const std::string& discount_reason = "");

    /**
     * @brief Process payment for bill
     * @param bill_id Bill's unique identifier
     * @param payment_amount Amount being paid
     * @param payment_method Method of payment
     * @return ServiceResult with operation status
     */
    ServiceResult process_payment(const std::string& bill_id, double payment_amount,
                                 const std::string& payment_method);

    /**
     * @brief Get bills for a specific patient
     * @param patient_id Patient's unique identifier
     * @param include_paid Whether to include paid bills
     * @return Vector of bills for the patient
     */
    std::vector<Bill> get_patient_bills(const std::string& patient_id, bool include_paid = true);

    /**
     * @brief Get bills for a specific doctor
     * @param doctor_id Doctor's unique identifier
     * @param include_paid Whether to include paid bills
     * @return Vector of bills for the doctor
     */
    std::vector<Bill> get_doctor_bills(const std::string& doctor_id, bool include_paid = true);

    /**
     * @brief Get unpaid bills
     * @param offset Number of records to skip
     * @param limit Maximum number of records to return
     * @return Vector of unpaid bills
     */
    std::vector<Bill> get_unpaid_bills(int offset = 0, int limit = 100);

    /**
     * @brief Get bills within date range
     * @param start_date Start date for range
     * @param end_date End date for range
     * @return Vector of bills within the date range
     */
    std::vector<Bill> get_bills_by_date_range(const Core::DateTime& start_date,
                                             const Core::DateTime& end_date);

    /**
     * @brief Calculate total revenue for a date range
     * @param start_date Start date for calculation
     * @param end_date End date for calculation
     * @return Total revenue amount
     */
    double calculate_revenue(const Core::DateTime& start_date, const Core::DateTime& end_date);

    /**
     * @brief Get financial summary for a period
     * @param start_date Start date for summary
     * @param end_date End date for summary
     * @return JSON object with financial summary
     */
    nlohmann::json get_financial_summary(const Core::DateTime& start_date,
                                        const Core::DateTime& end_date);

    /**
     * @brief Generate invoice for appointment
     * @param appointment_id Appointment's unique identifier
     * @param consultation_fee Consultation fee amount
     * @param medicines Vector of prescribed medicines with quantities
     * @return ServiceResult with operation status and generated bill ID
     */
    ServiceResult generate_appointment_invoice(const std::string& appointment_id,
                                              double consultation_fee,
                                              const std::vector<std::pair<Entities::Medicine, int>>& medicines);

    /**
     * @brief Export bill to JSON
     * @param bill_id Bill's unique identifier
     * @return JSON string representation of bill
     */
    std::string export_bill_to_json(const std::string& bill_id);

    /**
     * @brief Generate bill receipt
     * @param bill_id Bill's unique identifier
     * @return Formatted receipt string
     */
    std::string generate_receipt(const std::string& bill_id);

    /**
     * @brief Get billing statistics
     * @return JSON object with various billing statistics
     */
    nlohmann::json get_billing_statistics();

    /**
     * @brief Check if bill exists
     * @param bill_id Bill's unique identifier
     * @return True if bill exists, false otherwise
     */
    bool bill_exists(const std::string& bill_id);

private:
    Core::Database& database_;
    Core::Logger& logger_;
    static constexpr double DEFAULT_TAX_RATE = 0.10; // 10% tax rate

    /**
     * @brief Convert database row to Bill object
     * @param row Database row data
     * @return Bill object created from row data
     */
    Bill row_to_bill(const Core::Database::Row& row);

    /**
     * @brief Generate unique bill ID
     * @return Unique bill identifier
     */
    std::string generate_bill_id();

    /**
     * @brief Calculate bill totals
     * @param bill Bill object to calculate totals for
     */
    void calculate_bill_totals(Bill& bill);

    /**
     * @brief Save bill items to database
     * @param bill_id Bill's unique identifier
     * @param items Vector of bill items
     * @return True if successful, false otherwise
     */
    bool save_bill_items(const std::string& bill_id, const std::vector<BillItem>& items);

    /**
     * @brief Load bill items from database
     * @param bill_id Bill's unique identifier
     * @return Vector of bill items
     */
    std::vector<BillItem> load_bill_items(const std::string& bill_id);

    /**
     * @brief Log service operation
     * @param operation Operation name
     * @param bill_id Bill ID involved
     * @param success Operation success status
     * @param message Additional message
     */
    void log_operation(const std::string& operation, const std::string& bill_id,
                      bool success, const std::string& message = "");
};

} // namespace Services
} // namespace HospitalSystem
