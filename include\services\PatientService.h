#pragma once

#include <string>
#include <vector>
#include <memory>
#include <optional>
#include "entities/Patient.h"
#include "core/Database.h"
#include "core/Logger.h"

namespace HospitalSystem {
namespace Services {

/**
 * @brief Service class for managing patient operations
 * 
 * This class provides CRUD operations and business logic for patient management
 * including registration, updates, searches, and medical history management.
 */
class PatientService {
public:
    /**
     * @brief Result structure for service operations
     */
    struct ServiceResult {
        bool success;
        std::string message;
        int affected_rows;
        
        ServiceResult(bool s = false, const std::string& m = "", int rows = 0)
            : success(s), message(m), affected_rows(rows) {}
    };

    /**
     * @brief Constructor
     * @param database Reference to database instance
     * @param logger Reference to logger instance
     */
    PatientService(Core::Database& database, Core::Logger& logger);

    /**
     * @brief Destructor
     */
    ~PatientService() = default;

    // Disable copy constructor and assignment operator
    PatientService(const PatientService&) = delete;
    PatientService& operator=(const PatientService&) = delete;

    /**
     * @brief Register a new patient
     * @param patient Patient object to register
     * @return ServiceResult with operation status and generated patient ID
     */
    ServiceResult register_patient(Entities::Patient& patient);

    /**
     * @brief Get patient by ID
     * @param patient_id Patient's unique identifier
     * @return Optional Patient object (empty if not found)
     */
    std::optional<Entities::Patient> get_patient(const std::string& patient_id);

    /**
     * @brief Get patient by database ID
     * @param id Database ID
     * @return Optional Patient object (empty if not found)
     */
    std::optional<Entities::Patient> get_patient_by_id(int id);

    /**
     * @brief Update existing patient information
     * @param patient Patient object with updated information
     * @return ServiceResult with operation status
     */
    ServiceResult update_patient(const Entities::Patient& patient);

    /**
     * @brief Delete patient by ID
     * @param patient_id Patient's unique identifier
     * @return ServiceResult with operation status
     */
    ServiceResult delete_patient(const std::string& patient_id);

    /**
     * @brief Search patients by name
     * @param name_query Name or partial name to search for
     * @return Vector of matching patients
     */
    std::vector<Entities::Patient> search_patients_by_name(const std::string& name_query);

    /**
     * @brief Search patients by phone number
     * @param phone Phone number to search for
     * @return Vector of matching patients
     */
    std::vector<Entities::Patient> search_patients_by_phone(const std::string& phone);

    /**
     * @brief Search patients by email
     * @param email Email address to search for
     * @return Vector of matching patients
     */
    std::vector<Entities::Patient> search_patients_by_email(const std::string& email);

    /**
     * @brief Get all patients with pagination
     * @param offset Number of records to skip
     * @param limit Maximum number of records to return
     * @return Vector of patients
     */
    std::vector<Entities::Patient> get_all_patients(int offset = 0, int limit = 100);

    /**
     * @brief Get total count of patients
     * @return Total number of patients in database
     */
    int get_patient_count();

    /**
     * @brief Get patients by age range
     * @param min_age Minimum age (inclusive)
     * @param max_age Maximum age (inclusive)
     * @return Vector of patients within age range
     */
    std::vector<Entities::Patient> get_patients_by_age_range(int min_age, int max_age);

    /**
     * @brief Get patients by gender
     * @param gender Gender to filter by
     * @return Vector of patients with specified gender
     */
    std::vector<Entities::Patient> get_patients_by_gender(const std::string& gender);

    /**
     * @brief Add medical record to patient's history
     * @param patient_id Patient's unique identifier
     * @param medical_record Medical record to add
     * @return ServiceResult with operation status
     */
    ServiceResult add_medical_record(const std::string& patient_id, const std::string& medical_record);

    /**
     * @brief Add allergy to patient's allergy list
     * @param patient_id Patient's unique identifier
     * @param allergy Allergy to add
     * @return ServiceResult with operation status
     */
    ServiceResult add_patient_allergy(const std::string& patient_id, const std::string& allergy);

    /**
     * @brief Remove allergy from patient's allergy list
     * @param patient_id Patient's unique identifier
     * @param allergy Allergy to remove
     * @return ServiceResult with operation status
     */
    ServiceResult remove_patient_allergy(const std::string& patient_id, const std::string& allergy);

    /**
     * @brief Check if patient exists
     * @param patient_id Patient's unique identifier
     * @return True if patient exists, false otherwise
     */
    bool patient_exists(const std::string& patient_id);

    /**
     * @brief Validate patient data before operations
     * @param patient Patient object to validate
     * @return ServiceResult with validation status
     */
    ServiceResult validate_patient(const Entities::Patient& patient);

    /**
     * @brief Get patients with specific allergy
     * @param allergy Allergy to search for
     * @return Vector of patients with the specified allergy
     */
    std::vector<Entities::Patient> get_patients_with_allergy(const std::string& allergy);

    /**
     * @brief Export patient data to JSON
     * @param patient_id Patient's unique identifier
     * @return JSON string representation of patient data
     */
    std::string export_patient_data(const std::string& patient_id);

    /**
     * @brief Import patient data from JSON
     * @param json_data JSON string containing patient data
     * @return ServiceResult with import status
     */
    ServiceResult import_patient_data(const std::string& json_data);

    /**
     * @brief Get patient statistics
     * @return JSON object with various patient statistics
     */
    nlohmann::json get_patient_statistics();

private:
    Core::Database& database_;
    Core::Logger& logger_;

    /**
     * @brief Convert database row to Patient object
     * @param row Database row data
     * @return Patient object created from row data
     */
    Entities::Patient row_to_patient(const Core::Database::Row& row);

    /**
     * @brief Generate unique patient ID
     * @return Unique patient identifier
     */
    std::string generate_patient_id();

    /**
     * @brief Log service operation
     * @param operation Operation name
     * @param patient_id Patient ID involved
     * @param success Operation success status
     * @param message Additional message
     */
    void log_operation(const std::string& operation, const std::string& patient_id, 
                      bool success, const std::string& message = "");
};

} // namespace Services
} // namespace HospitalSystem
