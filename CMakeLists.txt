cmake_minimum_required(VERSION 3.16)
project(HospitalManagementSystem LANGUAGES CXX)

# Set C++17 standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Set build type if not specified
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Debug)
endif()

# Compiler-specific options
if(MSVC)
    add_compile_options(/W4)
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# Find required packages
find_package(SQLite3 REQUIRED)

# Try to find nlohmann_json, if not found, use FetchContent
find_package(nlohmann_json 3.9.1 QUIET)
if(NOT nlohmann_json_FOUND)
    include(FetchContent)
    FetchContent_Declare(
        nlohmann_json
        GIT_REPOSITORY https://github.com/nlohmann/json.git
        GIT_TAG v3.11.3
    )
    FetchContent_MakeAvailable(nlohmann_json)
endif()

# Include directories
include_directories(
    ${PROJECT_SOURCE_DIR}/include
)

# Collect source files
file(GLOB_RECURSE CORE_SOURCES "src/core/*.cpp")
file(GLOB_RECURSE ENTITY_SOURCES "src/entities/*.cpp")
file(GLOB_RECURSE SERVICE_SOURCES "src/services/*.cpp")
file(GLOB_RECURSE HEADERS "include/*.h")

set(ALL_SOURCES
    ${CORE_SOURCES}
    ${ENTITY_SOURCES}
    ${SERVICE_SOURCES}
    src/main.cpp
)

# Create main executable
add_executable(HospitalManagementSystem 
    ${ALL_SOURCES}
    ${HEADERS}
)

# Link libraries
target_link_libraries(HospitalManagementSystem
    PRIVATE
    SQLite::SQLite3
    nlohmann_json::nlohmann_json
)

# Set target properties
set_target_properties(HospitalManagementSystem PROPERTIES
    OUTPUT_NAME "hospital_system"
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Copy data files to build directory
file(COPY ${PROJECT_SOURCE_DIR}/data/ 
     DESTINATION ${CMAKE_BINARY_DIR}/data/)

# Testing configuration
option(BUILD_TESTS "Build tests" OFF)
if(BUILD_TESTS)
    enable_testing()
    
    # Try to find Catch2, if not found, use FetchContent
    find_package(Catch2 3 QUIET)
    if(NOT Catch2_FOUND)
        include(FetchContent)
        FetchContent_Declare(
            Catch2
            GIT_REPOSITORY https://github.com/catchorg/Catch2.git
            GIT_TAG v3.4.0
        )
        FetchContent_MakeAvailable(Catch2)
    endif()
    
    # Unit tests
    file(GLOB_RECURSE UNIT_TEST_SOURCES "tests/unit_tests/*.cpp")
    if(UNIT_TEST_SOURCES)
        add_executable(unit_tests
            ${CORE_SOURCES}
            ${ENTITY_SOURCES}
            ${SERVICE_SOURCES}
            ${UNIT_TEST_SOURCES}
        )
        
        target_link_libraries(unit_tests
            PRIVATE
            SQLite::SQLite3
            nlohmann_json::nlohmann_json
            Catch2::Catch2WithMain
        )
        
        target_include_directories(unit_tests PRIVATE ${PROJECT_SOURCE_DIR}/include)
        
        add_test(NAME UnitTests COMMAND unit_tests)
    endif()
    
    # Integration tests
    file(GLOB_RECURSE INTEGRATION_TEST_SOURCES "tests/integration_tests/*.cpp")
    if(INTEGRATION_TEST_SOURCES)
        add_executable(integration_tests
            ${CORE_SOURCES}
            ${ENTITY_SOURCES}
            ${SERVICE_SOURCES}
            ${INTEGRATION_TEST_SOURCES}
        )
        
        target_link_libraries(integration_tests
            PRIVATE
            SQLite::SQLite3
            nlohmann_json::nlohmann_json
            Catch2::Catch2WithMain
        )
        
        target_include_directories(integration_tests PRIVATE ${PROJECT_SOURCE_DIR}/include)
        
        add_test(NAME IntegrationTests COMMAND integration_tests)
    endif()
endif()

# Installation
install(TARGETS HospitalManagementSystem
    RUNTIME DESTINATION bin
)

install(DIRECTORY data/
    DESTINATION share/hospital_system/data
)

# Print configuration summary
message(STATUS "Hospital Management System Configuration:")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Build Tests: ${BUILD_TESTS}")
message(STATUS "  SQLite3 Found: ${SQLite3_FOUND}")
message(STATUS "  nlohmann_json Found: ${nlohmann_json_FOUND}")
