{"database": {"type": "sqlite", "filename": "hospital.db", "connection_timeout": 30, "enable_foreign_keys": true, "enable_wal_mode": true, "backup_interval_hours": 24, "backup_directory": "./backups"}, "logging": {"level": "INFO", "console_output": true, "file_output": true, "log_filename": "hospital.log", "max_file_size_mb": 10, "max_backup_files": 5, "log_format": "[%timestamp%] [%level%] %message%"}, "application": {"name": "Hospital Management System", "version": "1.0.0", "build_date": "2024-01-01", "timezone": "UTC", "date_format": "YYYY-MM-DD", "time_format": "HH:MM:SS", "datetime_format": "YYYY-MM-DD HH:MM:SS"}, "business_rules": {"patient": {"min_age": 0, "max_age": 150, "required_fields": ["name", "age", "gender"], "id_prefix": "PAT", "id_length": 8}, "doctor": {"required_fields": ["name", "specialization"], "id_prefix": "DOC", "id_length": 8, "max_qualifications": 10, "working_hours": {"start": "08:00", "end": "18:00"}}, "appointment": {"id_prefix": "APT", "id_length": 10, "min_duration_minutes": 15, "max_duration_minutes": 120, "advance_booking_days": 30, "cancellation_hours": 24, "working_days": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"], "working_hours": {"start": "08:00", "end": "18:00"}}, "medicine": {"id_prefix": "MED", "id_length": 8, "low_stock_threshold": 10, "required_fields": ["name", "description"]}, "billing": {"id_prefix": "BILL", "id_length": 10, "tax_rate": 0.1, "currency": "USD", "currency_symbol": "$", "decimal_places": 2, "payment_methods": ["Cash", "Credit Card", "Debit Card", "Insurance", "Bank Transfer"]}}, "security": {"password_policy": {"min_length": 8, "require_uppercase": true, "require_lowercase": true, "require_numbers": true, "require_special_chars": true}, "session": {"timeout_minutes": 30, "max_concurrent_sessions": 3}, "data_encryption": {"enabled": false, "algorithm": "AES-256"}}, "ui": {"theme": "default", "language": "en", "date_picker_format": "YYYY-MM-DD", "pagination": {"default_page_size": 25, "max_page_size": 100}, "table_settings": {"show_row_numbers": true, "enable_sorting": true, "enable_filtering": true}}, "notifications": {"enabled": true, "email": {"enabled": false, "smtp_server": "", "smtp_port": 587, "username": "", "password": "", "from_address": "<EMAIL>"}, "appointment_reminders": {"enabled": true, "hours_before": [24, 2]}, "billing_reminders": {"enabled": true, "days_overdue": [7, 14, 30]}}, "backup": {"enabled": true, "schedule": "daily", "time": "02:00", "retention_days": 30, "compression": true, "encryption": false}, "performance": {"database_pool_size": 10, "query_timeout_seconds": 30, "cache_enabled": true, "cache_size_mb": 50, "cache_ttl_minutes": 15}, "features": {"patient_management": true, "doctor_management": true, "appointment_scheduling": true, "billing_system": true, "inventory_management": true, "reporting": true, "data_export": true, "data_import": true, "audit_trail": true}, "validation": {"phone_regex": "^[+]?[1-9]?[0-9]{7,15}$", "email_regex": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", "name_regex": "^[a-zA-Z\\s'-]{2,50}$", "id_regex": "^[A-Z]{3}[0-9]{5,8}$"}, "reports": {"default_format": "PDF", "supported_formats": ["PDF", "Excel", "CSV", "JSON"], "template_directory": "./templates", "output_directory": "./reports"}, "integration": {"external_apis": {"enabled": false, "timeout_seconds": 30, "retry_attempts": 3}, "third_party_systems": {"laboratory": {"enabled": false, "api_endpoint": "", "api_key": ""}, "pharmacy": {"enabled": false, "api_endpoint": "", "api_key": ""}}}}