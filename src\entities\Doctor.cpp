#include "entities/Doctor.h"
#include "core/Utils.h"
#include <algorithm>
#include <sstream>
#include <ctime>

namespace HospitalSystem {
namespace Entities {

// TimeSlot implementation
std::string Doctor::TimeSlot::to_string() const {
    std::ostringstream oss;
    oss << std::setfill('0') << std::setw(2) << start_hour << ":"
        << std::setfill('0') << std::setw(2) << start_minute << " - "
        << std::setfill('0') << std::setw(2) << end_hour << ":"
        << std::setfill('0') << std::setw(2) << end_minute;
    return oss.str();
}

bool Doctor::TimeSlot::is_valid() const {
    if (start_hour < 0 || start_hour > 23 || end_hour < 0 || end_hour > 23) {
        return false;
    }
    if (start_minute < 0 || start_minute > 59 || end_minute < 0 || end_minute > 59) {
        return false;
    }
    
    int start_total_minutes = start_hour * 60 + start_minute;
    int end_total_minutes = end_hour * 60 + end_minute;
    
    return start_total_minutes < end_total_minutes;
}

bool Doctor::TimeSlot::contains_time(int hour, int minute) const {
    if (!is_valid()) {
        return false;
    }
    
    int check_total_minutes = hour * 60 + minute;
    int start_total_minutes = start_hour * 60 + start_minute;
    int end_total_minutes = end_hour * 60 + end_minute;
    
    return check_total_minutes >= start_total_minutes && check_total_minutes < end_total_minutes;
}

// Doctor implementation
Doctor::Doctor() {
    initialize_defaults();
}

Doctor::Doctor(const std::string& doctor_id, const std::string& name, 
               const std::string& specialization) 
    : doctor_id_(doctor_id), name_(name), specialization_(specialization) {
    initialize_defaults();
}

Doctor::Doctor(const std::string& doctor_id, const std::string& name, 
               const std::string& specialization, const std::string& phone,
               const std::string& email)
    : doctor_id_(doctor_id), name_(name), specialization_(specialization),
      phone_(phone), email_(email) {
    initialize_defaults();
}

void Doctor::initialize_defaults() {
    id_ = 0;
    created_at_ = Core::DateTime::now();
    updated_at_ = created_at_;
}

void Doctor::set_name(const std::string& name) {
    if (Core::ValidationUtils::is_valid_name(name)) {
        name_ = name;
        touch();
    }
}

void Doctor::set_specialization(const std::string& specialization) {
    if (!specialization.empty()) {
        specialization_ = specialization;
        touch();
    }
}

void Doctor::set_phone(const std::string& phone) {
    if (phone.empty() || Core::StringUtils::is_valid_phone(phone)) {
        phone_ = phone;
        touch();
    }
}

void Doctor::set_email(const std::string& email) {
    if (email.empty() || Core::StringUtils::is_valid_email(email)) {
        email_ = email;
        touch();
    }
}

void Doctor::set_qualifications(const std::vector<std::string>& qualifications) {
    qualifications_ = qualifications;
    touch();
}

void Doctor::add_qualification(const std::string& qualification) {
    if (!qualification.empty() && !has_qualification(qualification)) {
        qualifications_.push_back(qualification);
        touch();
    }
}

bool Doctor::remove_qualification(const std::string& qualification) {
    auto it = std::find(qualifications_.begin(), qualifications_.end(), qualification);
    if (it != qualifications_.end()) {
        qualifications_.erase(it);
        touch();
        return true;
    }
    return false;
}

bool Doctor::has_qualification(const std::string& qualification) const {
    return std::find(qualifications_.begin(), qualifications_.end(), qualification) != qualifications_.end();
}

void Doctor::set_availability(WeekDay day, const TimeSlot& time_slot) {
    if (time_slot.is_valid()) {
        schedule_[day] = time_slot;
        touch();
    }
}

void Doctor::remove_availability(WeekDay day) {
    schedule_.erase(day);
    touch();
}

bool Doctor::is_available(const Core::DateTime& date_time) const {
    WeekDay day = get_weekday_from_datetime(date_time);
    
    auto it = schedule_.find(day);
    if (it == schedule_.end()) {
        return false;
    }
    
    std::string time_str = date_time.to_time_string();
    int hour = std::stoi(time_str.substr(0, 2));
    int minute = std::stoi(time_str.substr(3, 2));
    
    return it->second.contains_time(hour, minute);
}

Doctor::TimeSlot Doctor::get_availability(WeekDay day) const {
    auto it = schedule_.find(day);
    if (it != schedule_.end()) {
        return it->second;
    }
    return TimeSlot(); // Empty time slot
}

bool Doctor::is_valid() const {
    return get_validation_errors().empty();
}

std::vector<std::string> Doctor::get_validation_errors() const {
    std::vector<std::string> errors;

    if (doctor_id_.empty()) {
        errors.push_back("Doctor ID cannot be empty");
    }

    if (!Core::ValidationUtils::is_valid_name(name_)) {
        errors.push_back("Invalid doctor name");
    }

    if (specialization_.empty()) {
        errors.push_back("Specialization cannot be empty");
    }

    if (!phone_.empty() && !Core::StringUtils::is_valid_phone(phone_)) {
        errors.push_back("Invalid phone number format");
    }

    if (!email_.empty() && !Core::StringUtils::is_valid_email(email_)) {
        errors.push_back("Invalid email format");
    }

    return errors;
}

nlohmann::json Doctor::to_json() const {
    nlohmann::json j;
    j["id"] = id_;
    j["doctor_id"] = doctor_id_;
    j["name"] = name_;
    j["specialization"] = specialization_;
    j["phone"] = phone_;
    j["email"] = email_;
    j["qualifications"] = qualifications_;
    
    nlohmann::json schedule_json;
    for (const auto& [day, time_slot] : schedule_) {
        nlohmann::json slot_json;
        slot_json["start_hour"] = time_slot.start_hour;
        slot_json["start_minute"] = time_slot.start_minute;
        slot_json["end_hour"] = time_slot.end_hour;
        slot_json["end_minute"] = time_slot.end_minute;
        schedule_json[weekday_to_string(day)] = slot_json;
    }
    j["schedule"] = schedule_json;
    
    j["created_at"] = created_at_.to_string();
    j["updated_at"] = updated_at_.to_string();
    return j;
}

Doctor Doctor::from_json(const nlohmann::json& json) {
    Doctor doctor;
    
    if (json.contains("id")) {
        doctor.set_id(json["id"]);
    }
    
    if (json.contains("doctor_id")) {
        doctor.set_doctor_id(json["doctor_id"]);
    }
    
    if (json.contains("name")) {
        doctor.set_name(json["name"]);
    }
    
    if (json.contains("specialization")) {
        doctor.set_specialization(json["specialization"]);
    }
    
    if (json.contains("phone")) {
        doctor.set_phone(json["phone"]);
    }
    
    if (json.contains("email")) {
        doctor.set_email(json["email"]);
    }
    
    if (json.contains("qualifications")) {
        doctor.set_qualifications(json["qualifications"]);
    }
    
    if (json.contains("schedule")) {
        for (const auto& [day_str, slot_json] : json["schedule"].items()) {
            try {
                WeekDay day = string_to_weekday(day_str);
                TimeSlot slot(
                    slot_json["start_hour"],
                    slot_json["start_minute"],
                    slot_json["end_hour"],
                    slot_json["end_minute"]
                );
                doctor.set_availability(day, slot);
            } catch (const std::exception&) {
                // Skip invalid schedule entries
            }
        }
    }
    
    if (json.contains("created_at")) {
        doctor.set_created_at(Core::DateTime(json["created_at"]));
    }
    
    if (json.contains("updated_at")) {
        doctor.set_updated_at(Core::DateTime(json["updated_at"]));
    }
    
    return doctor;
}

std::string Doctor::to_string() const {
    std::ostringstream oss;
    oss << "Doctor Information:\n";
    oss << "  ID: " << doctor_id_ << "\n";
    oss << "  Name: " << name_ << "\n";
    oss << "  Specialization: " << specialization_ << "\n";
    
    if (!phone_.empty()) {
        oss << "  Phone: " << phone_ << "\n";
    }
    
    if (!email_.empty()) {
        oss << "  Email: " << email_ << "\n";
    }
    
    if (!qualifications_.empty()) {
        oss << "  Qualifications: " << Core::StringUtils::join(qualifications_, ", ") << "\n";
    }
    
    if (!schedule_.empty()) {
        oss << "  Schedule:\n";
        for (const auto& [day, time_slot] : schedule_) {
            oss << "    " << weekday_to_string(day) << ": " << time_slot.to_string() << "\n";
        }
    }
    
    oss << "  Created: " << created_at_.to_string() << "\n";
    oss << "  Updated: " << updated_at_.to_string();
    
    return oss.str();
}

void Doctor::touch() {
    updated_at_ = Core::DateTime::now();
}

std::string Doctor::weekday_to_string(WeekDay day) {
    switch (day) {
        case WeekDay::MONDAY:    return "Monday";
        case WeekDay::TUESDAY:   return "Tuesday";
        case WeekDay::WEDNESDAY: return "Wednesday";
        case WeekDay::THURSDAY:  return "Thursday";
        case WeekDay::FRIDAY:    return "Friday";
        case WeekDay::SATURDAY:  return "Saturday";
        case WeekDay::SUNDAY:    return "Sunday";
        default:                 return "Unknown";
    }
}

Doctor::WeekDay Doctor::string_to_weekday(const std::string& day_str) {
    std::string lower_day = Core::StringUtils::to_lower(day_str);
    
    if (lower_day == "monday") return WeekDay::MONDAY;
    if (lower_day == "tuesday") return WeekDay::TUESDAY;
    if (lower_day == "wednesday") return WeekDay::WEDNESDAY;
    if (lower_day == "thursday") return WeekDay::THURSDAY;
    if (lower_day == "friday") return WeekDay::FRIDAY;
    if (lower_day == "saturday") return WeekDay::SATURDAY;
    if (lower_day == "sunday") return WeekDay::SUNDAY;
    
    throw std::invalid_argument("Invalid weekday string: " + day_str);
}

Doctor::WeekDay Doctor::get_weekday_from_datetime(const Core::DateTime& date_time) const {
    // Convert DateTime to time_t and then to tm structure
    std::string date_str = date_time.to_date_string();
    std::tm tm = {};
    std::istringstream ss(date_str);
    ss >> std::get_time(&tm, "%Y-%m-%d");
    
    std::time_t time = std::mktime(&tm);
    std::tm* local_tm = std::localtime(&time);
    
    // tm_wday: 0 = Sunday, 1 = Monday, ..., 6 = Saturday
    switch (local_tm->tm_wday) {
        case 0: return WeekDay::SUNDAY;
        case 1: return WeekDay::MONDAY;
        case 2: return WeekDay::TUESDAY;
        case 3: return WeekDay::WEDNESDAY;
        case 4: return WeekDay::THURSDAY;
        case 5: return WeekDay::FRIDAY;
        case 6: return WeekDay::SATURDAY;
        default: return WeekDay::MONDAY; // Default fallback
    }
}

bool Doctor::operator==(const Doctor& other) const {
    return doctor_id_ == other.doctor_id_;
}

bool Doctor::operator!=(const Doctor& other) const {
    return !(*this == other);
}

} // namespace Entities
} // namespace HospitalSystem
