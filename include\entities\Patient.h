#pragma once

#include <string>
#include <vector>
#include <nlohmann/json.hpp>
#include "core/Utils.h"

namespace HospitalSystem {
namespace Entities {

/**
 * @brief Patient entity class representing a hospital patient
 * 
 * This class encapsulates all patient-related information including
 * personal details, medical history, and contact information.
 */
class Patient {
public:
    /**
     * @brief Default constructor
     */
    Patient();

    /**
     * @brief Constructor with basic patient information
     * @param patient_id Unique patient identifier
     * @param name Patient's full name
     * @param age Patient's age
     * @param gender Patient's gender
     */
    Patient(const std::string& patient_id, const std::string& name, 
            int age, const std::string& gender);

    /**
     * @brief Constructor with complete patient information
     * @param patient_id Unique patient identifier
     * @param name Patient's full name
     * @param age Patient's age
     * @param gender Patient's gender
     * @param phone Patient's phone number
     * @param email Patient's email address
     * @param address Patient's address
     */
    Patient(const std::string& patient_id, const std::string& name, 
            int age, const std::string& gender, const std::string& phone,
            const std::string& email, const std::string& address);

    // Getters
    int get_id() const { return id_; }
    const std::string& get_patient_id() const { return patient_id_; }
    const std::string& get_name() const { return name_; }
    int get_age() const { return age_; }
    const std::string& get_gender() const { return gender_; }
    const std::string& get_phone() const { return phone_; }
    const std::string& get_email() const { return email_; }
    const std::string& get_address() const { return address_; }
    const std::string& get_medical_history() const { return medical_history_; }
    const std::vector<std::string>& get_allergies() const { return allergies_; }
    const Core::DateTime& get_created_at() const { return created_at_; }
    const Core::DateTime& get_updated_at() const { return updated_at_; }

    // Setters
    void set_id(int id) { id_ = id; }
    void set_patient_id(const std::string& patient_id) { patient_id_ = patient_id; }
    void set_name(const std::string& name);
    void set_age(int age);
    void set_gender(const std::string& gender);
    void set_phone(const std::string& phone);
    void set_email(const std::string& email);
    void set_address(const std::string& address);
    void set_medical_history(const std::string& medical_history);
    void set_allergies(const std::vector<std::string>& allergies);
    void set_created_at(const Core::DateTime& created_at) { created_at_ = created_at; }
    void set_updated_at(const Core::DateTime& updated_at) { updated_at_ = updated_at; }

    /**
     * @brief Add an allergy to the patient's allergy list
     * @param allergy Allergy to add
     */
    void add_allergy(const std::string& allergy);

    /**
     * @brief Remove an allergy from the patient's allergy list
     * @param allergy Allergy to remove
     * @return True if allergy was found and removed, false otherwise
     */
    bool remove_allergy(const std::string& allergy);

    /**
     * @brief Check if patient has a specific allergy
     * @param allergy Allergy to check for
     * @return True if patient has the allergy, false otherwise
     */
    bool has_allergy(const std::string& allergy) const;

    /**
     * @brief Update medical history with new information
     * @param new_history New medical history information to append
     */
    void update_medical_history(const std::string& new_history);

    /**
     * @brief Validate patient data
     * @return True if all patient data is valid, false otherwise
     */
    bool is_valid() const;

    /**
     * @brief Get validation errors
     * @return Vector of validation error messages
     */
    std::vector<std::string> get_validation_errors() const;

    /**
     * @brief Convert patient to JSON representation
     * @return JSON object representing the patient
     */
    nlohmann::json to_json() const;

    /**
     * @brief Create patient from JSON representation
     * @param json JSON object containing patient data
     * @return Patient object created from JSON
     * @throws std::invalid_argument if JSON is invalid
     */
    static Patient from_json(const nlohmann::json& json);

    /**
     * @brief Get patient's full information as formatted string
     * @return Formatted string with patient information
     */
    std::string to_string() const;

    /**
     * @brief Update the updated_at timestamp to current time
     */
    void touch();

    // Comparison operators
    bool operator==(const Patient& other) const;
    bool operator!=(const Patient& other) const;

private:
    int id_;                                    // Database ID (auto-increment)
    std::string patient_id_;                    // Unique patient identifier
    std::string name_;                          // Patient's full name
    int age_;                                   // Patient's age
    std::string gender_;                        // Patient's gender
    std::string phone_;                         // Patient's phone number
    std::string email_;                         // Patient's email address
    std::string address_;                       // Patient's address
    std::string medical_history_;               // Patient's medical history
    std::vector<std::string> allergies_;        // List of patient's allergies
    Core::DateTime created_at_;                 // Creation timestamp
    Core::DateTime updated_at_;                 // Last update timestamp

    /**
     * @brief Initialize default values
     */
    void initialize_defaults();
};

} // namespace Entities
} // namespace HospitalSystem
