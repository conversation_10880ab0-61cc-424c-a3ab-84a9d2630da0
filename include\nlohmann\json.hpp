// Simplified JSON header for compilation testing
// This is a minimal implementation for testing purposes only
// In production, use the full nlohmann/json library

#pragma once

#include <string>
#include <map>
#include <vector>
#include <iostream>
#include <sstream>

namespace nlohmann {
    class json {
    public:
        enum value_t {
            null,
            object,
            array,
            string,
            boolean,
            number_integer,
            number_unsigned,
            number_float
        };

    private:
        value_t m_type = null;
        std::string m_string_value;
        std::map<std::string, json> m_object_value;
        std::vector<json> m_array_value;
        int m_int_value = 0;
        double m_float_value = 0.0;
        bool m_bool_value = false;

    public:
        // Constructors
        json() : m_type(null) {}
        json(const std::string& value) : m_type(string), m_string_value(value) {}
        json(const char* value) : m_type(string), m_string_value(value) {}
        json(int value) : m_type(number_integer), m_int_value(value) {}
        json(double value) : m_type(number_float), m_float_value(value) {}
        json(bool value) : m_type(boolean), m_bool_value(value) {}

        // Assignment operators
        json& operator=(const std::string& value) {
            m_type = string;
            m_string_value = value;
            return *this;
        }

        json& operator=(int value) {
            m_type = number_integer;
            m_int_value = value;
            return *this;
        }

        json& operator=(double value) {
            m_type = number_float;
            m_float_value = value;
            return *this;
        }

        json& operator=(bool value) {
            m_type = boolean;
            m_bool_value = value;
            return *this;
        }

        // Array access
        json& operator[](const std::string& key) {
            if (m_type != object) {
                m_type = object;
                m_object_value.clear();
            }
            return m_object_value[key];
        }

        const json& operator[](const std::string& key) const {
            static json null_json;
            if (m_type == object) {
                auto it = m_object_value.find(key);
                if (it != m_object_value.end()) {
                    return it->second;
                }
            }
            return null_json;
        }

        json& operator[](size_t index) {
            if (m_type != array) {
                m_type = array;
                m_array_value.clear();
            }
            if (index >= m_array_value.size()) {
                m_array_value.resize(index + 1);
            }
            return m_array_value[index];
        }

        // Type checking
        bool is_null() const { return m_type == null; }
        bool is_string() const { return m_type == string; }
        bool is_number() const { return m_type == number_integer || m_type == number_float; }
        bool is_object() const { return m_type == object; }
        bool is_array() const { return m_type == array; }

        // Value access
        std::string get_string() const {
            if (m_type == string) return m_string_value;
            return "";
        }

        int get_int() const {
            if (m_type == number_integer) return m_int_value;
            if (m_type == number_float) return static_cast<int>(m_float_value);
            return 0;
        }

        double get_double() const {
            if (m_type == number_float) return m_float_value;
            if (m_type == number_integer) return static_cast<double>(m_int_value);
            return 0.0;
        }

        bool get_bool() const {
            if (m_type == boolean) return m_bool_value;
            return false;
        }

        // Conversion operators
        operator std::string() const { return get_string(); }
        operator int() const { return get_int(); }
        operator double() const { return get_double(); }
        operator bool() const { return get_bool(); }

        // Array operations
        void push_back(const json& value) {
            if (m_type != array) {
                m_type = array;
                m_array_value.clear();
            }
            m_array_value.push_back(value);
        }

        size_t size() const {
            if (m_type == array) return m_array_value.size();
            if (m_type == object) return m_object_value.size();
            return 0;
        }

        bool empty() const {
            return size() == 0;
        }

        // Iterator support (basic)
        std::vector<json>::iterator begin() {
            if (m_type == array) return m_array_value.begin();
            static std::vector<json> empty_vec;
            return empty_vec.begin();
        }

        std::vector<json>::iterator end() {
            if (m_type == array) return m_array_value.end();
            static std::vector<json> empty_vec;
            return empty_vec.end();
        }

        // Serialization (very basic)
        std::string dump(int indent = -1) const {
            std::ostringstream oss;
            switch (m_type) {
                case null:
                    oss << "null";
                    break;
                case string:
                    oss << "\"" << m_string_value << "\"";
                    break;
                case number_integer:
                    oss << m_int_value;
                    break;
                case number_float:
                    oss << m_float_value;
                    break;
                case boolean:
                    oss << (m_bool_value ? "true" : "false");
                    break;
                case object:
                    oss << "{";
                    for (auto it = m_object_value.begin(); it != m_object_value.end(); ++it) {
                        if (it != m_object_value.begin()) oss << ",";
                        oss << "\"" << it->first << "\":" << it->second.dump();
                    }
                    oss << "}";
                    break;
                case array:
                    oss << "[";
                    for (size_t i = 0; i < m_array_value.size(); ++i) {
                        if (i > 0) oss << ",";
                        oss << m_array_value[i].dump();
                    }
                    oss << "]";
                    break;
            }
            return oss.str();
        }

        // Static parse function (very basic)
        static json parse(const std::string& str) {
            // This is a very basic implementation
            // In production, use the full nlohmann/json parser
            json result;
            if (str == "null") {
                result.m_type = null;
            } else if (str == "true") {
                result.m_type = boolean;
                result.m_bool_value = true;
            } else if (str == "false") {
                result.m_type = boolean;
                result.m_bool_value = false;
            } else if (str.front() == '"' && str.back() == '"') {
                result.m_type = string;
                result.m_string_value = str.substr(1, str.length() - 2);
            } else {
                // Try to parse as number
                try {
                    if (str.find('.') != std::string::npos) {
                        result.m_type = number_float;
                        result.m_float_value = std::stod(str);
                    } else {
                        result.m_type = number_integer;
                        result.m_int_value = std::stoi(str);
                    }
                } catch (...) {
                    // Default to string if parsing fails
                    result.m_type = string;
                    result.m_string_value = str;
                }
            }
            return result;
        }
    };
}
