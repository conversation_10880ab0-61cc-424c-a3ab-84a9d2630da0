# Hospital Management System

A comprehensive Hospital Management System built with modern C++17, designed to manage patients, doctors, appointments, and billing operations efficiently.

## Features

- **Patient Management**: Register, update, search, and manage patient records
- **Doctor Management**: Manage doctor profiles, specializations, and schedules
- **Appointment Scheduling**: Schedule and manage patient appointments
- **Billing System**: Generate bills, process payments, and manage financial records
- **Medicine Inventory**: Track medicine stock and prescriptions
- **Reporting**: Generate various reports and statistics
- **Data Export/Import**: JSON-based data exchange capabilities

## Technology Stack

- **Language**: C++17
- **Build System**: CMake 3.16+
- **Database**: SQLite3
- **JSON Library**: nlohmann/json
- **Testing Framework**: Catch2
- **Architecture**: Modular design following SOLID principles

## Project Structure

```
hospital_management_system/
├── CMakeLists.txt              # Main CMake configuration
├── CMakePresets.json           # CMake presets for different configurations
├── config.json                 # Application configuration
├── README.md                   # This file
├── include/                    # Header files
│   ├── core/                   # Core system components
│   │   ├── Database.h          # Database wrapper and operations
│   │   ├── Logger.h            # Logging system
│   │   └── Utils.h             # Utility classes and functions
│   ├── entities/               # Entity classes
│   │   ├── Patient.h           # Patient entity
│   │   ├── Doctor.h            # Doctor entity
│   │   ├── Appointment.h       # Appointment entity
│   │   └── Medicine.h          # Medicine entity
│   └── services/               # Service layer
│       ├── PatientService.h    # Patient business logic
│       ├── DoctorService.h     # Doctor business logic
│       └── BillingService.h    # Billing business logic
├── src/                        # Source files
│   ├── core/                   # Core implementations
│   │   ├── Database.cpp
│   │   ├── Logger.cpp
│   │   └── Utils.cpp
│   ├── entities/               # Entity implementations
│   │   ├── Patient.cpp
│   │   ├── Doctor.cpp
│   │   ├── Appointment.cpp
│   │   └── Medicine.cpp
│   ├── services/               # Service implementations
│   │   ├── PatientService.cpp
│   │   ├── DoctorService.cpp
│   │   └── BillingService.cpp
│   └── main.cpp                # Main application entry point
└── tests/                      # Test files
    ├── unit/                   # Unit tests
    └── integration/            # Integration tests
```

## Prerequisites

- C++17 compatible compiler (GCC 7+, Clang 5+, MSVC 2017+)
- CMake 3.16 or higher
- SQLite3 development libraries
- Git (for dependency management)

### Installing Dependencies

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install build-essential cmake libsqlite3-dev git
```

#### CentOS/RHEL/Fedora
```bash
sudo yum install gcc-c++ cmake sqlite-devel git
# or for newer versions:
sudo dnf install gcc-c++ cmake sqlite-devel git
```

#### macOS
```bash
brew install cmake sqlite3 git
```

#### Windows
- Install Visual Studio 2017 or later with C++ support
- Install CMake from https://cmake.org/download/
- Install Git from https://git-scm.com/download/win

## Building the Project

### Using CMake Presets (Recommended)

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd hospital_management_system
   ```

2. **Configure for development**:
   ```bash
   cmake --preset=dev
   ```

3. **Build the project**:
   ```bash
   cmake --build --preset=dev
   ```

4. **Run tests**:
   ```bash
   ctest --preset=dev
   ```

### Manual CMake Build

1. **Create build directory**:
   ```bash
   mkdir build && cd build
   ```

2. **Configure the project**:
   ```bash
   cmake .. -DCMAKE_BUILD_TYPE=Debug
   ```

3. **Build**:
   ```bash
   cmake --build .
   ```

4. **Run tests**:
   ```bash
   ctest
   ```

## Running the Application

After building, run the main application:

```bash
# From build directory
./hospital_management_system

# Or from project root (if using presets)
./build/dev/hospital_management_system
```

## Configuration

The application uses `config.json` for configuration. Key settings include:

- **Database**: SQLite file location and settings
- **Logging**: Log levels and output destinations
- **Business Rules**: Validation rules and constraints
- **UI Settings**: Display preferences and pagination
- **Security**: Password policies and session management

## Database Schema

The system automatically creates the following tables:

- `patients`: Patient information and medical history
- `doctors`: Doctor profiles and qualifications
- `appointments`: Appointment scheduling and status
- `medicines`: Medicine inventory and details
- `prescriptions`: Prescribed medicines for patients
- `bills`: Billing and payment information

## Usage Examples

### Patient Management
```cpp
// Register a new patient
Entities::Patient patient("", "John Doe", 30, "Male", "************", "<EMAIL>");
auto result = patient_service.register_patient(patient);

// Search patients by name
auto patients = patient_service.search_patients_by_name("John");

// Get patient details
auto patient = patient_service.get_patient("PAT12345");
```

### Doctor Management
```cpp
// Register a new doctor
Entities::Doctor doctor("", "Dr. Smith", "Cardiology", "************", "<EMAIL>");
auto result = doctor_service.register_doctor(doctor);

// Set doctor availability
Entities::Doctor::TimeSlot morning_slot(9, 0, 12, 0);
doctor_service.set_doctor_availability("DOC12345", Entities::Doctor::WeekDay::MONDAY, morning_slot);
```

## Testing

The project includes comprehensive unit and integration tests:

```bash
# Run all tests
ctest

# Run specific test categories
ctest -R "unit"
ctest -R "integration"

# Run with verbose output
ctest --verbose
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Code Style Guidelines

- Follow C++17 best practices
- Use meaningful variable and function names
- Add comprehensive documentation for public APIs
- Include unit tests for new functionality
- Follow the existing project structure

## API Documentation

### Core Classes

- **Database**: SQLite wrapper with RAII and transaction support
- **Logger**: Thread-safe logging with multiple output destinations
- **DateTime**: Date/time utilities with ISO 8601 support
- **ValidationUtils**: Input validation and sanitization

### Entity Classes

- **Patient**: Patient information and medical history
- **Doctor**: Doctor profiles, qualifications, and schedules
- **Appointment**: Appointment scheduling and management
- **Medicine**: Medicine inventory and prescription tracking

### Service Classes

- **PatientService**: Patient CRUD operations and business logic
- **DoctorService**: Doctor management and scheduling
- **BillingService**: Billing, payments, and financial reporting

## Troubleshooting

### Common Issues

1. **Build Errors**:
   - Ensure C++17 compiler is available
   - Check CMake version (3.16+ required)
   - Verify SQLite3 development libraries are installed

2. **Database Issues**:
   - Check file permissions for database directory
   - Ensure SQLite3 is properly installed
   - Verify database schema initialization

3. **Runtime Errors**:
   - Check log files for detailed error messages
   - Verify configuration file is valid JSON
   - Ensure all required directories exist

### Getting Help

- Check the [Issues](../../issues) page for known problems
- Review the [Wiki](../../wiki) for additional documentation
- Contact the development team for support

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- SQLite team for the excellent embedded database
- nlohmann for the JSON library
- Catch2 team for the testing framework
- All contributors who helped improve this project

## Roadmap

### Version 1.1 (Planned)
- [ ] Web-based user interface
- [ ] Advanced reporting features
- [ ] Email notifications
- [ ] Data backup and restore
- [ ] Multi-user support with authentication

### Version 1.2 (Future)
- [ ] Mobile application
- [ ] Integration with external systems
- [ ] Advanced analytics and dashboards
- [ ] Multi-language support
- [ ] Cloud deployment options

---

**Hospital Management System v1.0**  
Built with ❤️ using modern C++17
