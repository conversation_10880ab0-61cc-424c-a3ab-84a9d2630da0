#include <catch2/catch_test_macros.hpp>
#include "entities/Patient.h"
#include "core/Utils.h"

using namespace HospitalSystem::Entities;
using namespace HospitalSystem::Core;

TEST_CASE("Patient Creation and Basic Operations", "[patient]") {
    SECTION("Default constructor creates valid patient") {
        Patient patient;
        
        REQUIRE(patient.get_id() == 0);
        REQUIRE(patient.get_patient_id().empty());
        REQUIRE(patient.get_name().empty());
        REQUIRE(patient.get_age() == 0);
        REQUIRE(patient.get_gender().empty());
        REQUIRE(patient.get_allergies().empty());
    }
    
    SECTION("Constructor with parameters creates patient correctly") {
        Patient patient("PAT12345", "<PERSON>", 30, "Male");
        
        REQUIRE(patient.get_patient_id() == "PAT12345");
        REQUIRE(patient.get_name() == "<PERSON>");
        REQUIRE(patient.get_age() == 30);
        REQUIRE(patient.get_gender() == "Male");
    }
    
    SECTION("Full constructor creates patient with all details") {
        Patient patient("PAT12345", "<PERSON>", 30, "Male", 
                       "************", "<EMAIL>", "123 Main St");
        
        REQUIRE(patient.get_patient_id() == "PAT12345");
        REQUIRE(patient.get_name() == "John Doe");
        REQUIRE(patient.get_age() == 30);
        REQUIRE(patient.get_gender() == "Male");
        REQUIRE(patient.get_phone() == "************");
        REQUIRE(patient.get_email() == "<EMAIL>");
        REQUIRE(patient.get_address() == "123 Main St");
    }
}

TEST_CASE("Patient Setters and Validation", "[patient]") {
    Patient patient;
    
    SECTION("Setting valid name works") {
        patient.set_name("Jane Smith");
        REQUIRE(patient.get_name() == "Jane Smith");
    }
    
    SECTION("Setting valid age works") {
        patient.set_age(25);
        REQUIRE(patient.get_age() == 25);
    }
    
    SECTION("Setting valid gender works") {
        patient.set_gender("Female");
        REQUIRE(patient.get_gender() == "Female");
    }
    
    SECTION("Setting valid phone works") {
        patient.set_phone("************");
        REQUIRE(patient.get_phone() == "************");
    }
    
    SECTION("Setting valid email works") {
        patient.set_email("<EMAIL>");
        REQUIRE(patient.get_email() == "<EMAIL>");
    }
}

TEST_CASE("Patient Allergy Management", "[patient]") {
    Patient patient("PAT12345", "John Doe", 30, "Male");
    
    SECTION("Adding allergies works") {
        patient.add_allergy("Penicillin");
        patient.add_allergy("Peanuts");
        
        REQUIRE(patient.get_allergies().size() == 2);
        REQUIRE(patient.has_allergy("Penicillin"));
        REQUIRE(patient.has_allergy("Peanuts"));
        REQUIRE_FALSE(patient.has_allergy("Shellfish"));
    }
    
    SECTION("Removing allergies works") {
        patient.add_allergy("Penicillin");
        patient.add_allergy("Peanuts");
        
        REQUIRE(patient.remove_allergy("Penicillin"));
        REQUIRE_FALSE(patient.has_allergy("Penicillin"));
        REQUIRE(patient.has_allergy("Peanuts"));
        REQUIRE(patient.get_allergies().size() == 1);
    }
    
    SECTION("Removing non-existent allergy returns false") {
        REQUIRE_FALSE(patient.remove_allergy("NonExistent"));
    }
    
    SECTION("Adding duplicate allergy is ignored") {
        patient.add_allergy("Penicillin");
        patient.add_allergy("Penicillin");
        
        REQUIRE(patient.get_allergies().size() == 1);
    }
}

TEST_CASE("Patient Medical History", "[patient]") {
    Patient patient("PAT12345", "John Doe", 30, "Male");
    
    SECTION("Setting medical history works") {
        patient.set_medical_history("Previous surgery in 2020");
        REQUIRE(patient.get_medical_history() == "Previous surgery in 2020");
    }
    
    SECTION("Updating medical history appends new entries") {
        patient.set_medical_history("Initial history");
        patient.update_medical_history("New entry");
        
        std::string history = patient.get_medical_history();
        REQUIRE(history.find("Initial history") != std::string::npos);
        REQUIRE(history.find("New entry") != std::string::npos);
    }
}

TEST_CASE("Patient Validation", "[patient]") {
    SECTION("Valid patient passes validation") {
        Patient patient("PAT12345", "John Doe", 30, "Male");
        REQUIRE(patient.is_valid());
        REQUIRE(patient.get_validation_errors().empty());
    }
    
    SECTION("Patient with empty ID fails validation") {
        Patient patient("", "John Doe", 30, "Male");
        REQUIRE_FALSE(patient.is_valid());
        
        auto errors = patient.get_validation_errors();
        REQUIRE_FALSE(errors.empty());
        REQUIRE(std::find_if(errors.begin(), errors.end(), 
                [](const std::string& error) {
                    return error.find("Patient ID cannot be empty") != std::string::npos;
                }) != errors.end());
    }
    
    SECTION("Patient with invalid age fails validation") {
        Patient patient("PAT12345", "John Doe", -5, "Male");
        REQUIRE_FALSE(patient.is_valid());
        
        auto errors = patient.get_validation_errors();
        REQUIRE_FALSE(errors.empty());
    }
}

TEST_CASE("Patient JSON Serialization", "[patient]") {
    SECTION("Patient can be converted to JSON") {
        Patient patient("PAT12345", "John Doe", 30, "Male", 
                       "************", "<EMAIL>", "123 Main St");
        patient.add_allergy("Penicillin");
        patient.set_medical_history("No significant history");
        
        auto json = patient.to_json();
        
        REQUIRE(json["patient_id"] == "PAT12345");
        REQUIRE(json["name"] == "John Doe");
        REQUIRE(json["age"] == 30);
        REQUIRE(json["gender"] == "Male");
        REQUIRE(json["phone"] == "************");
        REQUIRE(json["email"] == "<EMAIL>");
        REQUIRE(json["address"] == "123 Main St");
        REQUIRE(json["allergies"].size() == 1);
        REQUIRE(json["allergies"][0] == "Penicillin");
        REQUIRE(json["medical_history"] == "No significant history");
    }
    
    SECTION("Patient can be created from JSON") {
        nlohmann::json json = {
            {"patient_id", "PAT67890"},
            {"name", "Jane Smith"},
            {"age", 25},
            {"gender", "Female"},
            {"phone", "************"},
            {"email", "<EMAIL>"},
            {"address", "456 Oak Ave"},
            {"allergies", {"Peanuts", "Shellfish"}},
            {"medical_history", "Asthma since childhood"}
        };
        
        Patient patient = Patient::from_json(json);
        
        REQUIRE(patient.get_patient_id() == "PAT67890");
        REQUIRE(patient.get_name() == "Jane Smith");
        REQUIRE(patient.get_age() == 25);
        REQUIRE(patient.get_gender() == "Female");
        REQUIRE(patient.get_phone() == "************");
        REQUIRE(patient.get_email() == "<EMAIL>");
        REQUIRE(patient.get_address() == "456 Oak Ave");
        REQUIRE(patient.get_allergies().size() == 2);
        REQUIRE(patient.has_allergy("Peanuts"));
        REQUIRE(patient.has_allergy("Shellfish"));
        REQUIRE(patient.get_medical_history() == "Asthma since childhood");
    }
}

TEST_CASE("Patient String Representation", "[patient]") {
    SECTION("Patient to_string includes all information") {
        Patient patient("PAT12345", "John Doe", 30, "Male", 
                       "************", "<EMAIL>", "123 Main St");
        patient.add_allergy("Penicillin");
        patient.set_medical_history("No significant history");
        
        std::string str = patient.to_string();
        
        REQUIRE(str.find("PAT12345") != std::string::npos);
        REQUIRE(str.find("John Doe") != std::string::npos);
        REQUIRE(str.find("30") != std::string::npos);
        REQUIRE(str.find("Male") != std::string::npos);
        REQUIRE(str.find("************") != std::string::npos);
        REQUIRE(str.find("<EMAIL>") != std::string::npos);
        REQUIRE(str.find("123 Main St") != std::string::npos);
        REQUIRE(str.find("Penicillin") != std::string::npos);
        REQUIRE(str.find("No significant history") != std::string::npos);
    }
}

TEST_CASE("Patient Comparison Operators", "[patient]") {
    Patient patient1("PAT12345", "John Doe", 30, "Male");
    Patient patient2("PAT12345", "Jane Smith", 25, "Female");
    Patient patient3("PAT67890", "Bob Johnson", 40, "Male");
    
    SECTION("Patients with same ID are equal") {
        REQUIRE(patient1 == patient2);
        REQUIRE_FALSE(patient1 != patient2);
    }
    
    SECTION("Patients with different IDs are not equal") {
        REQUIRE_FALSE(patient1 == patient3);
        REQUIRE(patient1 != patient3);
    }
}
