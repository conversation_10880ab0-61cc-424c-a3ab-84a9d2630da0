#pragma once

#include <string>
#include <vector>
#include <chrono>
#include <random>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <cctype>
#include <regex>

namespace HospitalSystem {
namespace Core {

/**
 * @brief Date and time utility class
 */
class DateTime {
public:
    /**
     * @brief Default constructor - creates current date/time
     */
    DateTime();

    /**
     * @brief Constructor with specific date and time
     * @param year Year (e.g., 2023)
     * @param month Month (1-12)
     * @param day Day (1-31)
     * @param hour Hour (0-23)
     * @param minute Minute (0-59)
     * @param second Second (0-59)
     */
    DateTime(int year, int month, int day, int hour = 0, int minute = 0, int second = 0);

    /**
     * @brief Constructor from string (ISO 8601 format: YYYY-MM-DD HH:MM:SS)
     * @param datetime_str Date/time string
     */
    explicit DateTime(const std::string& datetime_str);

    /**
     * @brief Get current date/time
     * @return DateTime object representing current time
     */
    static DateTime now();

    /**
     * @brief Convert to string in ISO 8601 format
     * @return String representation (YYYY-MM-DD HH:MM:SS)
     */
    std::string to_string() const;

    /**
     * @brief Convert to date-only string
     * @return Date string (YYYY-MM-DD)
     */
    std::string to_date_string() const;

    /**
     * @brief Convert to time-only string
     * @return Time string (HH:MM:SS)
     */
    std::string to_time_string() const;

    /**
     * @brief Add days to current date
     * @param days Number of days to add
     * @return New DateTime object
     */
    DateTime add_days(int days) const;

    /**
     * @brief Add hours to current date/time
     * @param hours Number of hours to add
     * @return New DateTime object
     */
    DateTime add_hours(int hours) const;

    /**
     * @brief Add minutes to current date/time
     * @param minutes Number of minutes to add
     * @return New DateTime object
     */
    DateTime add_minutes(int minutes) const;

    /**
     * @brief Check if this date/time is before another
     * @param other Other DateTime to compare with
     * @return True if this is before other
     */
    bool is_before(const DateTime& other) const;

    /**
     * @brief Check if this date/time is after another
     * @param other Other DateTime to compare with
     * @return True if this is after other
     */
    bool is_after(const DateTime& other) const;

    /**
     * @brief Get difference in days between two dates
     * @param other Other DateTime
     * @return Number of days difference
     */
    int days_difference(const DateTime& other) const;

    // Comparison operators
    bool operator==(const DateTime& other) const;
    bool operator!=(const DateTime& other) const;
    bool operator<(const DateTime& other) const;
    bool operator<=(const DateTime& other) const;
    bool operator>(const DateTime& other) const;
    bool operator>=(const DateTime& other) const;

private:
    std::chrono::system_clock::time_point time_point_;
};

/**
 * @brief String utility functions
 */
class StringUtils {
public:
    /**
     * @brief Trim whitespace from both ends of string
     * @param str String to trim
     * @return Trimmed string
     */
    static std::string trim(const std::string& str);

    /**
     * @brief Convert string to lowercase
     * @param str String to convert
     * @return Lowercase string
     */
    static std::string to_lower(const std::string& str);

    /**
     * @brief Convert string to uppercase
     * @param str String to convert
     * @return Uppercase string
     */
    static std::string to_upper(const std::string& str);

    /**
     * @brief Split string by delimiter
     * @param str String to split
     * @param delimiter Delimiter character
     * @return Vector of split strings
     */
    static std::vector<std::string> split(const std::string& str, char delimiter);

    /**
     * @brief Join strings with delimiter
     * @param strings Vector of strings to join
     * @param delimiter Delimiter string
     * @return Joined string
     */
    static std::string join(const std::vector<std::string>& strings, const std::string& delimiter);

    /**
     * @brief Check if string starts with prefix
     * @param str String to check
     * @param prefix Prefix to look for
     * @return True if string starts with prefix
     */
    static bool starts_with(const std::string& str, const std::string& prefix);

    /**
     * @brief Check if string ends with suffix
     * @param str String to check
     * @param suffix Suffix to look for
     * @return True if string ends with suffix
     */
    static bool ends_with(const std::string& str, const std::string& suffix);

    /**
     * @brief Replace all occurrences of substring
     * @param str Original string
     * @param from Substring to replace
     * @param to Replacement string
     * @return String with replacements
     */
    static std::string replace_all(const std::string& str, const std::string& from, const std::string& to);

    /**
     * @brief Check if string contains only digits
     * @param str String to check
     * @return True if string contains only digits
     */
    static bool is_numeric(const std::string& str);

    /**
     * @brief Validate email format
     * @param email Email string to validate
     * @return True if email format is valid
     */
    static bool is_valid_email(const std::string& email);

    /**
     * @brief Validate phone number format
     * @param phone Phone number string to validate
     * @return True if phone format is valid
     */
    static bool is_valid_phone(const std::string& phone);
};

/**
 * @brief Validation utility functions
 */
class ValidationUtils {
public:
    /**
     * @brief Validate patient name
     * @param name Name to validate
     * @return True if name is valid
     */
    static bool is_valid_name(const std::string& name);

    /**
     * @brief Validate age
     * @param age Age to validate
     * @return True if age is valid (0-150)
     */
    static bool is_valid_age(int age);

    /**
     * @brief Validate gender
     * @param gender Gender string to validate
     * @return True if gender is valid (Male, Female, Other)
     */
    static bool is_valid_gender(const std::string& gender);

    /**
     * @brief Validate medical ID format
     * @param medical_id Medical ID to validate
     * @return True if medical ID format is valid
     */
    static bool is_valid_medical_id(const std::string& medical_id);

    /**
     * @brief Validate appointment time (must be in future and during working hours)
     * @param appointment_time DateTime to validate
     * @return True if appointment time is valid
     */
    static bool is_valid_appointment_time(const DateTime& appointment_time);
};

/**
 * @brief ID generation utility
 */
class IdGenerator {
public:
    /**
     * @brief Generate unique patient ID
     * @return Unique patient ID string
     */
    static std::string generate_patient_id();

    /**
     * @brief Generate unique doctor ID
     * @return Unique doctor ID string
     */
    static std::string generate_doctor_id();

    /**
     * @brief Generate unique appointment ID
     * @return Unique appointment ID string
     */
    static std::string generate_appointment_id();

    /**
     * @brief Generate random string of specified length
     * @param length Length of random string
     * @return Random string
     */
    static std::string generate_random_string(size_t length);

private:
    static std::random_device rd_;
    static std::mt19937 gen_;
    static std::uniform_int_distribution<> dis_;
};

} // namespace Core
} // namespace HospitalSystem
