#pragma once

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <sqlite3.h>

namespace HospitalSystem {
namespace Core {

/**
 * @brief Database management class for SQLite operations
 * 
 * This class provides a wrapper around SQLite3 C API with RAII principles,
 * transaction support, and prepared statement management.
 */
class Database {
public:
    /**
     * @brief Result structure for database operations
     */
    struct QueryResult {
        bool success;
        std::string error_message;
        int affected_rows;
        long long last_insert_id;
    };

    /**
     * @brief Row data type for query results
     */
    using Row = std::vector<std::string>;
    using ResultSet = std::vector<Row>;

    /**
     * @brief Constructor
     * @param db_path Path to the SQLite database file
     */
    explicit Database(const std::string& db_path);

    /**
     * @brief Destructor - automatically closes database connection
     */
    ~Database();

    // Disable copy constructor and assignment operator
    Database(const Database&) = delete;
    Database& operator=(const Database&) = delete;

    // Enable move constructor and assignment operator
    Database(Database&& other) noexcept;
    Database& operator=(Database&& other) noexcept;

    /**
     * @brief Open database connection
     * @return true if successful, false otherwise
     */
    bool open();

    /**
     * @brief Close database connection
     */
    void close();

    /**
     * @brief Check if database is open
     * @return true if database is open, false otherwise
     */
    bool is_open() const;

    /**
     * @brief Execute a simple SQL query without parameters
     * @param query SQL query string
     * @return QueryResult with operation status
     */
    QueryResult execute(const std::string& query);

    /**
     * @brief Execute a SELECT query and return results
     * @param query SQL SELECT query
     * @return pair of QueryResult and ResultSet
     */
    std::pair<QueryResult, ResultSet> select(const std::string& query);

    /**
     * @brief Execute a prepared statement with parameters
     * @param query SQL query with placeholders (?)
     * @param params Parameters to bind to the query
     * @return QueryResult with operation status
     */
    QueryResult execute_prepared(const std::string& query, 
                                const std::vector<std::string>& params);

    /**
     * @brief Execute a SELECT prepared statement with parameters
     * @param query SQL SELECT query with placeholders
     * @param params Parameters to bind to the query
     * @return pair of QueryResult and ResultSet
     */
    std::pair<QueryResult, ResultSet> select_prepared(const std::string& query,
                                                     const std::vector<std::string>& params);

    /**
     * @brief Begin a database transaction
     * @return true if successful, false otherwise
     */
    bool begin_transaction();

    /**
     * @brief Commit current transaction
     * @return true if successful, false otherwise
     */
    bool commit_transaction();

    /**
     * @brief Rollback current transaction
     * @return true if successful, false otherwise
     */
    bool rollback_transaction();

    /**
     * @brief Execute multiple statements within a transaction
     * @param statements Vector of SQL statements to execute
     * @return true if all statements executed successfully, false otherwise
     */
    bool execute_transaction(const std::vector<std::string>& statements);

    /**
     * @brief Initialize database schema for hospital management system
     * @return true if successful, false otherwise
     */
    bool initialize_schema();

    /**
     * @brief Get the last error message
     * @return Last error message from SQLite
     */
    std::string get_last_error() const;

    /**
     * @brief Escape string for SQL queries (prevent SQL injection)
     * @param input Input string to escape
     * @return Escaped string safe for SQL queries
     */
    static std::string escape_string(const std::string& input);

private:
    sqlite3* db_;
    std::string db_path_;
    bool is_open_;

    /**
     * @brief Prepare a SQL statement
     * @param query SQL query string
     * @return sqlite3_stmt pointer or nullptr on failure
     */
    sqlite3_stmt* prepare_statement(const std::string& query);

    /**
     * @brief Bind parameters to prepared statement
     * @param stmt Prepared statement
     * @param params Parameters to bind
     * @return true if successful, false otherwise
     */
    bool bind_parameters(sqlite3_stmt* stmt, const std::vector<std::string>& params);

    /**
     * @brief Execute prepared statement and collect results
     * @param stmt Prepared statement
     * @return pair of QueryResult and ResultSet
     */
    std::pair<QueryResult, ResultSet> execute_statement(sqlite3_stmt* stmt);
};

} // namespace Core
} // namespace HospitalSystem
