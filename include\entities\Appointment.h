#pragma once

#include <string>
#include <nlohmann/json.hpp>
#include "core/Utils.h"

namespace HospitalSystem {
namespace Entities {

/**
 * @brief Appointment entity class representing a medical appointment
 * 
 * This class encapsulates all appointment-related information including
 * patient and doctor references, scheduling details, and status.
 */
class Appointment {
public:
    /**
     * @brief Enumeration for appointment status
     */
    enum class Status {
        SCHEDULED,      // Appointment is scheduled
        CONFIRMED,      // Appointment is confirmed by patient
        IN_PROGRESS,    // Appointment is currently happening
        COMPLETED,      // Appointment has been completed
        CANCELLED,      // Appointment has been cancelled
        NO_SHOW        // <PERSON><PERSON> didn't show up
    };

    /**
     * @brief Default constructor
     */
    Appointment();

    /**
     * @brief Constructor with basic appointment information
     * @param appointment_id Unique appointment identifier
     * @param patient_id Patient's unique identifier
     * @param doctor_id Doctor's unique identifier
     * @param appointment_datetime Date and time of the appointment
     */
    Appointment(const std::string& appointment_id, const std::string& patient_id,
                const std::string& doctor_id, const Core::DateTime& appointment_datetime);

    /**
     * @brief Constructor with complete appointment information
     * @param appointment_id Unique appointment identifier
     * @param patient_id Patient's unique identifier
     * @param doctor_id Doctor's unique identifier
     * @param appointment_datetime Date and time of the appointment
     * @param status Appointment status
     * @param notes Additional notes for the appointment
     */
    Appointment(const std::string& appointment_id, const std::string& patient_id,
                const std::string& doctor_id, const Core::DateTime& appointment_datetime,
                Status status, const std::string& notes);

    // Getters
    int get_id() const { return id_; }
    const std::string& get_appointment_id() const { return appointment_id_; }
    const std::string& get_patient_id() const { return patient_id_; }
    const std::string& get_doctor_id() const { return doctor_id_; }
    const Core::DateTime& get_appointment_datetime() const { return appointment_datetime_; }
    Status get_status() const { return status_; }
    const std::string& get_notes() const { return notes_; }
    const Core::DateTime& get_created_at() const { return created_at_; }
    const Core::DateTime& get_updated_at() const { return updated_at_; }

    // Setters
    void set_id(int id) { id_ = id; }
    void set_appointment_id(const std::string& appointment_id) { appointment_id_ = appointment_id; }
    void set_patient_id(const std::string& patient_id);
    void set_doctor_id(const std::string& doctor_id);
    void set_appointment_datetime(const Core::DateTime& appointment_datetime);
    void set_status(Status status);
    void set_notes(const std::string& notes);
    void set_created_at(const Core::DateTime& created_at) { created_at_ = created_at; }
    void set_updated_at(const Core::DateTime& updated_at) { updated_at_ = updated_at; }

    /**
     * @brief Check if appointment can be cancelled
     * @return True if appointment can be cancelled, false otherwise
     */
    bool can_be_cancelled() const;

    /**
     * @brief Check if appointment can be rescheduled
     * @return True if appointment can be rescheduled, false otherwise
     */
    bool can_be_rescheduled() const;

    /**
     * @brief Check if appointment is in the past
     * @return True if appointment datetime is in the past, false otherwise
     */
    bool is_past_due() const;

    /**
     * @brief Check if appointment is today
     * @return True if appointment is scheduled for today, false otherwise
     */
    bool is_today() const;

    /**
     * @brief Get time until appointment in minutes
     * @return Minutes until appointment (negative if in the past)
     */
    int minutes_until_appointment() const;

    /**
     * @brief Cancel the appointment
     * @param reason Reason for cancellation (optional)
     * @return True if successfully cancelled, false otherwise
     */
    bool cancel(const std::string& reason = "");

    /**
     * @brief Confirm the appointment
     * @return True if successfully confirmed, false otherwise
     */
    bool confirm();

    /**
     * @brief Mark appointment as in progress
     * @return True if successfully marked, false otherwise
     */
    bool start();

    /**
     * @brief Complete the appointment
     * @param completion_notes Notes about the completed appointment
     * @return True if successfully completed, false otherwise
     */
    bool complete(const std::string& completion_notes = "");

    /**
     * @brief Mark appointment as no-show
     * @return True if successfully marked, false otherwise
     */
    bool mark_no_show();

    /**
     * @brief Reschedule the appointment
     * @param new_datetime New date and time for the appointment
     * @return True if successfully rescheduled, false otherwise
     */
    bool reschedule(const Core::DateTime& new_datetime);

    /**
     * @brief Validate appointment data
     * @return True if all appointment data is valid, false otherwise
     */
    bool is_valid() const;

    /**
     * @brief Get validation errors
     * @return Vector of validation error messages
     */
    std::vector<std::string> get_validation_errors() const;

    /**
     * @brief Convert appointment to JSON representation
     * @return JSON object representing the appointment
     */
    nlohmann::json to_json() const;

    /**
     * @brief Create appointment from JSON representation
     * @param json JSON object containing appointment data
     * @return Appointment object created from JSON
     * @throws std::invalid_argument if JSON is invalid
     */
    static Appointment from_json(const nlohmann::json& json);

    /**
     * @brief Get appointment's full information as formatted string
     * @return Formatted string with appointment information
     */
    std::string to_string() const;

    /**
     * @brief Update the updated_at timestamp to current time
     */
    void touch();

    /**
     * @brief Convert Status enum to string
     * @param status Status enum value
     * @return String representation of the status
     */
    static std::string status_to_string(Status status);

    /**
     * @brief Convert string to Status enum
     * @param status_str String representation of the status
     * @return Status enum value
     * @throws std::invalid_argument if string is invalid
     */
    static Status string_to_status(const std::string& status_str);

    // Comparison operators
    bool operator==(const Appointment& other) const;
    bool operator!=(const Appointment& other) const;
    bool operator<(const Appointment& other) const;  // For sorting by datetime

private:
    int id_;                                    // Database ID (auto-increment)
    std::string appointment_id_;                // Unique appointment identifier
    std::string patient_id_;                    // Patient's unique identifier
    std::string doctor_id_;                     // Doctor's unique identifier
    Core::DateTime appointment_datetime_;       // Date and time of appointment
    Status status_;                             // Current status of appointment
    std::string notes_;                         // Additional notes
    Core::DateTime created_at_;                 // Creation timestamp
    Core::DateTime updated_at_;                 // Last update timestamp

    /**
     * @brief Initialize default values
     */
    void initialize_defaults();
};

} // namespace Entities
} // namespace HospitalSystem
