#include "entities/Medicine.h"
#include "core/Utils.h"
#include <algorithm>
#include <sstream>
#include <iomanip>

namespace HospitalSystem {
namespace Entities {

Medicine::Medicine() {
    initialize_defaults();
}

Medicine::Medicine(const std::string& medicine_id, const std::string& name, 
                   const std::string& description)
    : medicine_id_(medicine_id), name_(name), description_(description) {
    initialize_defaults();
}

Medicine::Medicine(const std::string& medicine_id, const std::string& name, 
                   const std::string& description, const std::string& dosage,
                   int stock_quantity, double unit_price)
    : medicine_id_(medicine_id), name_(name), description_(description),
      dosage_(dosage), stock_quantity_(stock_quantity), unit_price_(unit_price) {
    initialize_defaults();
}

void Medicine::initialize_defaults() {
    id_ = 0;
    if (stock_quantity_ < 0) stock_quantity_ = 0;
    if (unit_price_ < 0.0) unit_price_ = 0.0;
    created_at_ = Core::DateTime::now();
    updated_at_ = created_at_;
}

void Medicine::set_name(const std::string& name) {
    if (!name.empty()) {
        name_ = name;
        touch();
    }
}

void Medicine::set_description(const std::string& description) {
    description_ = description;
    touch();
}

void Medicine::set_dosage(const std::string& dosage) {
    dosage_ = dosage;
    touch();
}

void Medicine::set_side_effects(const std::vector<std::string>& side_effects) {
    side_effects_ = side_effects;
    touch();
}

void Medicine::set_contraindications(const std::vector<std::string>& contraindications) {
    contraindications_ = contraindications;
    touch();
}

void Medicine::set_stock_quantity(int stock_quantity) {
    if (stock_quantity >= 0) {
        stock_quantity_ = stock_quantity;
        touch();
    }
}

void Medicine::set_unit_price(double unit_price) {
    if (unit_price >= 0.0) {
        unit_price_ = unit_price;
        touch();
    }
}

void Medicine::add_side_effect(const std::string& side_effect) {
    if (!side_effect.empty() && !has_side_effect(side_effect)) {
        side_effects_.push_back(side_effect);
        touch();
    }
}

bool Medicine::remove_side_effect(const std::string& side_effect) {
    auto it = std::find(side_effects_.begin(), side_effects_.end(), side_effect);
    if (it != side_effects_.end()) {
        side_effects_.erase(it);
        touch();
        return true;
    }
    return false;
}

bool Medicine::has_side_effect(const std::string& side_effect) const {
    return std::find(side_effects_.begin(), side_effects_.end(), side_effect) != side_effects_.end();
}

void Medicine::add_contraindication(const std::string& contraindication) {
    if (!contraindication.empty() && !has_contraindication(contraindication)) {
        contraindications_.push_back(contraindication);
        touch();
    }
}

bool Medicine::remove_contraindication(const std::string& contraindication) {
    auto it = std::find(contraindications_.begin(), contraindications_.end(), contraindication);
    if (it != contraindications_.end()) {
        contraindications_.erase(it);
        touch();
        return true;
    }
    return false;
}

bool Medicine::has_contraindication(const std::string& contraindication) const {
    return std::find(contraindications_.begin(), contraindications_.end(), contraindication) != contraindications_.end();
}

bool Medicine::add_stock(int quantity) {
    if (quantity < 0) {
        return false;
    }
    
    stock_quantity_ += quantity;
    touch();
    return true;
}

bool Medicine::remove_stock(int quantity) {
    if (quantity < 0 || quantity > stock_quantity_) {
        return false;
    }
    
    stock_quantity_ -= quantity;
    touch();
    return true;
}

bool Medicine::is_in_stock() const {
    return stock_quantity_ > 0;
}

bool Medicine::is_low_stock(int min_threshold) const {
    return stock_quantity_ < min_threshold;
}

double Medicine::calculate_stock_value() const {
    return stock_quantity_ * unit_price_;
}

bool Medicine::is_safe_for_patient(const std::vector<std::string>& patient_allergies) const {
    // Check if any patient allergy matches any contraindication
    for (const auto& allergy : patient_allergies) {
        if (has_contraindication(allergy)) {
            return false;
        }
    }
    return true;
}

bool Medicine::is_valid() const {
    return get_validation_errors().empty();
}

std::vector<std::string> Medicine::get_validation_errors() const {
    std::vector<std::string> errors;

    if (medicine_id_.empty()) {
        errors.push_back("Medicine ID cannot be empty");
    }

    if (name_.empty()) {
        errors.push_back("Medicine name cannot be empty");
    }

    if (stock_quantity_ < 0) {
        errors.push_back("Stock quantity cannot be negative");
    }

    if (unit_price_ < 0.0) {
        errors.push_back("Unit price cannot be negative");
    }

    return errors;
}

nlohmann::json Medicine::to_json() const {
    nlohmann::json j;
    j["id"] = id_;
    j["medicine_id"] = medicine_id_;
    j["name"] = name_;
    j["description"] = description_;
    j["dosage"] = dosage_;
    j["side_effects"] = side_effects_;
    j["contraindications"] = contraindications_;
    j["stock_quantity"] = stock_quantity_;
    j["unit_price"] = unit_price_;
    j["created_at"] = created_at_.to_string();
    j["updated_at"] = updated_at_.to_string();
    return j;
}

Medicine Medicine::from_json(const nlohmann::json& json) {
    Medicine medicine;
    
    if (json.contains("id")) {
        medicine.set_id(json["id"]);
    }
    
    if (json.contains("medicine_id")) {
        medicine.set_medicine_id(json["medicine_id"]);
    }
    
    if (json.contains("name")) {
        medicine.set_name(json["name"]);
    }
    
    if (json.contains("description")) {
        medicine.set_description(json["description"]);
    }
    
    if (json.contains("dosage")) {
        medicine.set_dosage(json["dosage"]);
    }
    
    if (json.contains("side_effects")) {
        medicine.set_side_effects(json["side_effects"]);
    }
    
    if (json.contains("contraindications")) {
        medicine.set_contraindications(json["contraindications"]);
    }
    
    if (json.contains("stock_quantity")) {
        medicine.set_stock_quantity(json["stock_quantity"]);
    }
    
    if (json.contains("unit_price")) {
        medicine.set_unit_price(json["unit_price"]);
    }
    
    if (json.contains("created_at")) {
        medicine.set_created_at(Core::DateTime(json["created_at"]));
    }
    
    if (json.contains("updated_at")) {
        medicine.set_updated_at(Core::DateTime(json["updated_at"]));
    }
    
    return medicine;
}

std::string Medicine::to_string() const {
    std::ostringstream oss;
    oss << "Medicine Information:\n";
    oss << "  ID: " << medicine_id_ << "\n";
    oss << "  Name: " << name_ << "\n";
    
    if (!description_.empty()) {
        oss << "  Description: " << description_ << "\n";
    }
    
    if (!dosage_.empty()) {
        oss << "  Dosage: " << dosage_ << "\n";
    }
    
    if (!side_effects_.empty()) {
        oss << "  Side Effects: " << Core::StringUtils::join(side_effects_, ", ") << "\n";
    }
    
    if (!contraindications_.empty()) {
        oss << "  Contraindications: " << Core::StringUtils::join(contraindications_, ", ") << "\n";
    }
    
    oss << "  Stock: " << stock_quantity_ << " units\n";
    oss << "  Unit Price: $" << std::fixed << std::setprecision(2) << unit_price_ << "\n";
    oss << "  Stock Value: $" << std::fixed << std::setprecision(2) << calculate_stock_value() << "\n";
    oss << "  Status: " << get_stock_status() << "\n";
    oss << "  Created: " << created_at_.to_string() << "\n";
    oss << "  Updated: " << updated_at_.to_string();
    
    return oss.str();
}

std::string Medicine::get_stock_status() const {
    if (!is_in_stock()) {
        return "Out of Stock";
    } else if (is_low_stock()) {
        return "Low Stock";
    } else {
        return "In Stock";
    }
}

void Medicine::touch() {
    updated_at_ = Core::DateTime::now();
}

bool Medicine::operator==(const Medicine& other) const {
    return medicine_id_ == other.medicine_id_;
}

bool Medicine::operator!=(const Medicine& other) const {
    return !(*this == other);
}

} // namespace Entities
} // namespace HospitalSystem
