#include <iostream>
#include <string>

// Simple test to check if basic compilation works
int main() {
    std::cout << "Hospital Management System - Compilation Test" << std::endl;
    std::cout << "C++17 features test:" << std::endl;
    
    // Test auto keyword
    auto message = std::string("Hello from C++17!");
    std::cout << "Auto: " << message << std::endl;
    
    // Test structured bindings (C++17 feature)
    auto pair = std::make_pair(42, "Answer");
    auto [number, text] = pair;
    std::cout << "Structured binding: " << number << " - " << text << std::endl;
    
    // Test if constexpr (C++17 feature)
    if constexpr (true) {
        std::cout << "Constexpr if works!" << std::endl;
    }
    
    std::cout << "Basic compilation successful!" << std::endl;
    return 0;
}
