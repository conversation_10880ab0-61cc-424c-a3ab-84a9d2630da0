#include "core/Logger.h"
#include <iostream>
#include <iomanip>

namespace HospitalSystem {
namespace Core {

// Static member initialization
std::unique_ptr<Logger> Logger::instance_ = nullptr;
std::mutex Logger::instance_mutex_;

Logger::Logger(const std::string& log_file_path, LogLevel min_level)
    : log_file_path_(log_file_path), min_level_(min_level), 
      console_output_enabled_(true), timestamp_enabled_(true) {
    
    if (!log_file_path_.empty()) {
        open_log_file();
    }
}

Logger::~Logger() {
    flush();
}

Logger::Logger(Logger&& other) noexcept
    : log_file_path_(std::move(other.log_file_path_)),
      log_file_(std::move(other.log_file_)),
      min_level_(other.min_level_),
      console_output_enabled_(other.console_output_enabled_),
      timestamp_enabled_(other.timestamp_enabled_) {
}

Logger& Logger::operator=(Logger&& other) noexcept {
    if (this != &other) {
        flush();
        log_file_path_ = std::move(other.log_file_path_);
        log_file_ = std::move(other.log_file_);
        min_level_ = other.min_level_;
        console_output_enabled_ = other.console_output_enabled_;
        timestamp_enabled_ = other.timestamp_enabled_;
    }
    return *this;
}

void Logger::set_log_level(LogLevel level) {
    std::lock_guard<std::mutex> lock(mutex_);
    min_level_ = level;
}

LogLevel Logger::get_log_level() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return min_level_;
}

void Logger::debug(const std::string& message) {
    log(LogLevel::DEBUG, message);
}

void Logger::info(const std::string& message) {
    log(LogLevel::INFO, message);
}

void Logger::warning(const std::string& message) {
    log(LogLevel::WARNING, message);
}

void Logger::error(const std::string& message) {
    log(LogLevel::ERROR, message);
}

void Logger::critical(const std::string& message) {
    log(LogLevel::CRITICAL, message);
}

void Logger::log(LogLevel level, const std::string& message) {
    if (level < min_level_) {
        return;
    }
    
    write_log(level, message);
}

void Logger::flush() {
    std::lock_guard<std::mutex> lock(mutex_);
    if (log_file_ && log_file_->is_open()) {
        log_file_->flush();
    }
    std::cout.flush();
    std::cerr.flush();
}

void Logger::enable_console_output(bool enable) {
    std::lock_guard<std::mutex> lock(mutex_);
    console_output_enabled_ = enable;
}

void Logger::enable_timestamp(bool enable) {
    std::lock_guard<std::mutex> lock(mutex_);
    timestamp_enabled_ = enable;
}

Logger& Logger::get_instance() {
    std::lock_guard<std::mutex> lock(instance_mutex_);
    if (!instance_) {
        instance_ = std::make_unique<Logger>();
    }
    return *instance_;
}

void Logger::initialize(const std::string& log_file_path, LogLevel min_level) {
    std::lock_guard<std::mutex> lock(instance_mutex_);
    instance_ = std::make_unique<Logger>(log_file_path, min_level);
}

std::string Logger::get_timestamp() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;
    
    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    oss << '.' << std::setfill('0') << std::setw(3) << ms.count();
    
    return oss.str();
}

std::string Logger::level_to_string(LogLevel level) const {
    switch (level) {
        case LogLevel::DEBUG:    return "DEBUG";
        case LogLevel::INFO:     return "INFO";
        case LogLevel::WARNING:  return "WARNING";
        case LogLevel::ERROR:    return "ERROR";
        case LogLevel::CRITICAL: return "CRITICAL";
        default:                 return "UNKNOWN";
    }
}

void Logger::write_log(LogLevel level, const std::string& message) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    std::ostringstream log_line;
    
    if (timestamp_enabled_) {
        log_line << "[" << get_timestamp() << "] ";
    }
    
    log_line << "[" << level_to_string(level) << "] " << message;
    
    std::string final_message = log_line.str();
    
    // Write to console
    if (console_output_enabled_) {
        if (level >= LogLevel::ERROR) {
            std::cerr << final_message << std::endl;
        } else {
            std::cout << final_message << std::endl;
        }
    }
    
    // Write to file
    if (log_file_ && log_file_->is_open()) {
        *log_file_ << final_message << std::endl;
    }
}

bool Logger::open_log_file() {
    try {
        log_file_ = std::make_unique<std::ofstream>(log_file_path_, 
            std::ios::out | std::ios::app);
        
        if (!log_file_->is_open()) {
            std::cerr << "Failed to open log file: " << log_file_path_ << std::endl;
            return false;
        }
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Exception opening log file: " << e.what() << std::endl;
        return false;
    }
}

// Helper function implementations for template formatting
void format_string(std::ostringstream& oss, const std::string& format) {
    oss << format;
}

} // namespace Core
} // namespace HospitalSystem
