#include <catch2/catch_test_macros.hpp>
#include "core/Database.h"
#include "entities/Patient.h"
#include "entities/Doctor.h"
#include <filesystem>

using namespace HospitalSystem::Core;
using namespace HospitalSystem::Entities;

class DatabaseTestFixture {
public:
    DatabaseTestFixture() : db("test_hospital.db") {
        // Clean up any existing test database
        if (std::filesystem::exists("test_hospital.db")) {
            std::filesystem::remove("test_hospital.db");
        }
        
        // Initialize database
        if (!db.initialize()) {
            throw std::runtime_error("Failed to initialize test database");
        }
        
        // Initialize schema
        if (!db.initialize_schema()) {
            throw std::runtime_error("Failed to initialize test database schema");
        }
    }
    
    ~DatabaseTestFixture() {
        // Clean up test database
        db.close();
        if (std::filesystem::exists("test_hospital.db")) {
            std::filesystem::remove("test_hospital.db");
        }
    }
    
    Database db;
};

TEST_CASE_METHOD(DatabaseTestFixture, "Database Initialization", "[database]") {
    SECTION("Database is initialized and connected") {
        REQUIRE(db.is_connected());
    }
    
    SECTION("Schema tables are created") {
        // Check if tables exist by trying to query them
        auto result = db.execute_query("SELECT name FROM sqlite_master WHERE type='table'");
        REQUIRE(result.success);
        REQUIRE(result.rows.size() >= 6); // Should have at least 6 tables
        
        // Check for specific tables
        bool has_patients = false, has_doctors = false, has_appointments = false;
        bool has_medicines = false, has_prescriptions = false, has_bills = false;
        
        for (const auto& row : result.rows) {
            std::string table_name = row.at("name");
            if (table_name == "patients") has_patients = true;
            else if (table_name == "doctors") has_doctors = true;
            else if (table_name == "appointments") has_appointments = true;
            else if (table_name == "medicines") has_medicines = true;
            else if (table_name == "prescriptions") has_prescriptions = true;
            else if (table_name == "bills") has_bills = true;
        }
        
        REQUIRE(has_patients);
        REQUIRE(has_doctors);
        REQUIRE(has_appointments);
        REQUIRE(has_medicines);
        REQUIRE(has_prescriptions);
        REQUIRE(has_bills);
    }
}

TEST_CASE_METHOD(DatabaseTestFixture, "Database CRUD Operations", "[database]") {
    SECTION("Insert and select operations work") {
        // Insert a test patient
        std::string sql = "INSERT INTO patients (patient_id, name, age, gender, phone, email, address, medical_history, allergies, created_at, updated_at) "
                         "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        std::vector<std::string> params = {
            "PAT12345", "John Doe", "30", "Male", "************", 
            "<EMAIL>", "123 Main St", "No history", "[]",
            "2024-01-01 10:00:00", "2024-01-01 10:00:00"
        };
        
        auto insert_result = db.execute_query(sql, params);
        REQUIRE(insert_result.success);
        REQUIRE(insert_result.affected_rows == 1);
        
        // Select the inserted patient
        auto select_result = db.execute_query("SELECT * FROM patients WHERE patient_id = ?", {"PAT12345"});
        REQUIRE(select_result.success);
        REQUIRE(select_result.rows.size() == 1);
        
        const auto& row = select_result.rows[0];
        REQUIRE(row.at("patient_id") == "PAT12345");
        REQUIRE(row.at("name") == "John Doe");
        REQUIRE(row.at("age") == "30");
        REQUIRE(row.at("gender") == "Male");
    }
    
    SECTION("Update operations work") {
        // First insert a patient
        std::string insert_sql = "INSERT INTO patients (patient_id, name, age, gender, created_at, updated_at) "
                                "VALUES (?, ?, ?, ?, ?, ?)";
        
        auto insert_result = db.execute_query(insert_sql, {
            "PAT67890", "Jane Smith", "25", "Female", 
            "2024-01-01 10:00:00", "2024-01-01 10:00:00"
        });
        REQUIRE(insert_result.success);
        
        // Update the patient
        std::string update_sql = "UPDATE patients SET name = ?, age = ? WHERE patient_id = ?";
        auto update_result = db.execute_query(update_sql, {"Jane Doe", "26", "PAT67890"});
        REQUIRE(update_result.success);
        REQUIRE(update_result.affected_rows == 1);
        
        // Verify the update
        auto select_result = db.execute_query("SELECT name, age FROM patients WHERE patient_id = ?", {"PAT67890"});
        REQUIRE(select_result.success);
        REQUIRE(select_result.rows.size() == 1);
        
        const auto& row = select_result.rows[0];
        REQUIRE(row.at("name") == "Jane Doe");
        REQUIRE(row.at("age") == "26");
    }
    
    SECTION("Delete operations work") {
        // First insert a patient
        std::string insert_sql = "INSERT INTO patients (patient_id, name, age, gender, created_at, updated_at) "
                                "VALUES (?, ?, ?, ?, ?, ?)";
        
        auto insert_result = db.execute_query(insert_sql, {
            "PAT99999", "Test Patient", "30", "Male", 
            "2024-01-01 10:00:00", "2024-01-01 10:00:00"
        });
        REQUIRE(insert_result.success);
        
        // Delete the patient
        auto delete_result = db.execute_query("DELETE FROM patients WHERE patient_id = ?", {"PAT99999"});
        REQUIRE(delete_result.success);
        REQUIRE(delete_result.affected_rows == 1);
        
        // Verify the deletion
        auto select_result = db.execute_query("SELECT * FROM patients WHERE patient_id = ?", {"PAT99999"});
        REQUIRE(select_result.success);
        REQUIRE(select_result.rows.empty());
    }
}

TEST_CASE_METHOD(DatabaseTestFixture, "Database Transaction Support", "[database]") {
    SECTION("Transaction commit works") {
        REQUIRE(db.begin_transaction());
        
        // Insert multiple records in transaction
        auto result1 = db.execute_query(
            "INSERT INTO patients (patient_id, name, age, gender, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)",
            {"PAT11111", "Patient 1", "30", "Male", "2024-01-01 10:00:00", "2024-01-01 10:00:00"}
        );
        REQUIRE(result1.success);
        
        auto result2 = db.execute_query(
            "INSERT INTO patients (patient_id, name, age, gender, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)",
            {"PAT22222", "Patient 2", "25", "Female", "2024-01-01 10:00:00", "2024-01-01 10:00:00"}
        );
        REQUIRE(result2.success);
        
        REQUIRE(db.commit_transaction());
        
        // Verify both records exist
        auto select_result = db.execute_query("SELECT COUNT(*) as count FROM patients WHERE patient_id IN (?, ?)", 
                                            {"PAT11111", "PAT22222"});
        REQUIRE(select_result.success);
        REQUIRE(select_result.rows[0].at("count") == "2");
    }
    
    SECTION("Transaction rollback works") {
        REQUIRE(db.begin_transaction());
        
        // Insert a record
        auto result = db.execute_query(
            "INSERT INTO patients (patient_id, name, age, gender, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)",
            {"PAT33333", "Patient 3", "40", "Male", "2024-01-01 10:00:00", "2024-01-01 10:00:00"}
        );
        REQUIRE(result.success);
        
        REQUIRE(db.rollback_transaction());
        
        // Verify record doesn't exist after rollback
        auto select_result = db.execute_query("SELECT * FROM patients WHERE patient_id = ?", {"PAT33333"});
        REQUIRE(select_result.success);
        REQUIRE(select_result.rows.empty());
    }
}

TEST_CASE_METHOD(DatabaseTestFixture, "Database Error Handling", "[database]") {
    SECTION("Invalid SQL returns error") {
        auto result = db.execute_query("INVALID SQL STATEMENT");
        REQUIRE_FALSE(result.success);
        REQUIRE_FALSE(result.error_message.empty());
    }
    
    SECTION("Constraint violation returns error") {
        // Insert a patient
        auto result1 = db.execute_query(
            "INSERT INTO patients (patient_id, name, age, gender, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)",
            {"PAT44444", "Patient 4", "30", "Male", "2024-01-01 10:00:00", "2024-01-01 10:00:00"}
        );
        REQUIRE(result1.success);
        
        // Try to insert another patient with same ID (should fail due to unique constraint)
        auto result2 = db.execute_query(
            "INSERT INTO patients (patient_id, name, age, gender, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)",
            {"PAT44444", "Another Patient", "25", "Female", "2024-01-01 10:00:00", "2024-01-01 10:00:00"}
        );
        REQUIRE_FALSE(result2.success);
        REQUIRE_FALSE(result2.error_message.empty());
    }
}

TEST_CASE_METHOD(DatabaseTestFixture, "Database Performance", "[database]") {
    SECTION("Bulk insert performance") {
        auto start_time = std::chrono::high_resolution_clock::now();
        
        REQUIRE(db.begin_transaction());
        
        // Insert 100 patients
        for (int i = 0; i < 100; ++i) {
            std::string patient_id = "PAT" + std::to_string(10000 + i);
            std::string name = "Patient " + std::to_string(i);
            
            auto result = db.execute_query(
                "INSERT INTO patients (patient_id, name, age, gender, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)",
                {patient_id, name, "30", "Male", "2024-01-01 10:00:00", "2024-01-01 10:00:00"}
            );
            REQUIRE(result.success);
        }
        
        REQUIRE(db.commit_transaction());
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        // Should complete within reasonable time (less than 1 second for 100 inserts)
        REQUIRE(duration.count() < 1000);
        
        // Verify all records were inserted
        auto count_result = db.execute_query("SELECT COUNT(*) as count FROM patients");
        REQUIRE(count_result.success);
        REQUIRE(std::stoi(count_result.rows[0].at("count")) >= 100);
    }
}
