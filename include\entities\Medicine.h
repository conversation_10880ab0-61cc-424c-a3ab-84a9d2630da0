#pragma once

#include <string>
#include <vector>
#include <nlohmann/json.hpp>
#include "core/Utils.h"

namespace HospitalSystem {
namespace Entities {

/**
 * @brief Medicine entity class representing a pharmaceutical product
 * 
 * This class encapsulates all medicine-related information including
 * details, dosage, side effects, and inventory management.
 */
class Medicine {
public:
    /**
     * @brief Default constructor
     */
    Medicine();

    /**
     * @brief Constructor with basic medicine information
     * @param medicine_id Unique medicine identifier
     * @param name Medicine name
     * @param description Medicine description
     */
    Medicine(const std::string& medicine_id, const std::string& name, 
             const std::string& description);

    /**
     * @brief Constructor with complete medicine information
     * @param medicine_id Unique medicine identifier
     * @param name Medicine name
     * @param description Medicine description
     * @param dosage Standard dosage information
     * @param stock_quantity Current stock quantity
     * @param unit_price Price per unit
     */
    Medicine(const std::string& medicine_id, const std::string& name, 
             const std::string& description, const std::string& dosage,
             int stock_quantity, double unit_price);

    // Getters
    int get_id() const { return id_; }
    const std::string& get_medicine_id() const { return medicine_id_; }
    const std::string& get_name() const { return name_; }
    const std::string& get_description() const { return description_; }
    const std::string& get_dosage() const { return dosage_; }
    const std::vector<std::string>& get_side_effects() const { return side_effects_; }
    const std::vector<std::string>& get_contraindications() const { return contraindications_; }
    int get_stock_quantity() const { return stock_quantity_; }
    double get_unit_price() const { return unit_price_; }
    const Core::DateTime& get_created_at() const { return created_at_; }
    const Core::DateTime& get_updated_at() const { return updated_at_; }

    // Setters
    void set_id(int id) { id_ = id; }
    void set_medicine_id(const std::string& medicine_id) { medicine_id_ = medicine_id; }
    void set_name(const std::string& name);
    void set_description(const std::string& description);
    void set_dosage(const std::string& dosage);
    void set_side_effects(const std::vector<std::string>& side_effects);
    void set_contraindications(const std::vector<std::string>& contraindications);
    void set_stock_quantity(int stock_quantity);
    void set_unit_price(double unit_price);
    void set_created_at(const Core::DateTime& created_at) { created_at_ = created_at; }
    void set_updated_at(const Core::DateTime& updated_at) { updated_at_ = updated_at; }

    /**
     * @brief Add a side effect to the medicine's side effects list
     * @param side_effect Side effect to add
     */
    void add_side_effect(const std::string& side_effect);

    /**
     * @brief Remove a side effect from the medicine's side effects list
     * @param side_effect Side effect to remove
     * @return True if side effect was found and removed, false otherwise
     */
    bool remove_side_effect(const std::string& side_effect);

    /**
     * @brief Check if medicine has a specific side effect
     * @param side_effect Side effect to check for
     * @return True if medicine has the side effect, false otherwise
     */
    bool has_side_effect(const std::string& side_effect) const;

    /**
     * @brief Add a contraindication to the medicine's contraindications list
     * @param contraindication Contraindication to add
     */
    void add_contraindication(const std::string& contraindication);

    /**
     * @brief Remove a contraindication from the medicine's contraindications list
     * @param contraindication Contraindication to remove
     * @return True if contraindication was found and removed, false otherwise
     */
    bool remove_contraindication(const std::string& contraindication);

    /**
     * @brief Check if medicine has a specific contraindication
     * @param contraindication Contraindication to check for
     * @return True if medicine has the contraindication, false otherwise
     */
    bool has_contraindication(const std::string& contraindication) const;

    /**
     * @brief Increase stock quantity
     * @param quantity Quantity to add to stock
     * @return True if successful, false if quantity is negative
     */
    bool add_stock(int quantity);

    /**
     * @brief Decrease stock quantity
     * @param quantity Quantity to remove from stock
     * @return True if successful, false if insufficient stock or negative quantity
     */
    bool remove_stock(int quantity);

    /**
     * @brief Check if medicine is in stock
     * @return True if stock quantity > 0, false otherwise
     */
    bool is_in_stock() const;

    /**
     * @brief Check if medicine stock is low (less than minimum threshold)
     * @param min_threshold Minimum stock threshold (default: 10)
     * @return True if stock is below threshold, false otherwise
     */
    bool is_low_stock(int min_threshold = 10) const;

    /**
     * @brief Calculate total value of current stock
     * @return Total value (stock_quantity * unit_price)
     */
    double calculate_stock_value() const;

    /**
     * @brief Check if medicine can be prescribed to a patient with given allergies
     * @param patient_allergies Vector of patient's allergies
     * @return True if safe to prescribe, false if contraindicated
     */
    bool is_safe_for_patient(const std::vector<std::string>& patient_allergies) const;

    /**
     * @brief Validate medicine data
     * @return True if all medicine data is valid, false otherwise
     */
    bool is_valid() const;

    /**
     * @brief Get validation errors
     * @return Vector of validation error messages
     */
    std::vector<std::string> get_validation_errors() const;

    /**
     * @brief Convert medicine to JSON representation
     * @return JSON object representing the medicine
     */
    nlohmann::json to_json() const;

    /**
     * @brief Create medicine from JSON representation
     * @param json JSON object containing medicine data
     * @return Medicine object created from JSON
     * @throws std::invalid_argument if JSON is invalid
     */
    static Medicine from_json(const nlohmann::json& json);

    /**
     * @brief Get medicine's full information as formatted string
     * @return Formatted string with medicine information
     */
    std::string to_string() const;

    /**
     * @brief Get medicine's stock status as string
     * @return String describing stock status
     */
    std::string get_stock_status() const;

    /**
     * @brief Update the updated_at timestamp to current time
     */
    void touch();

    // Comparison operators
    bool operator==(const Medicine& other) const;
    bool operator!=(const Medicine& other) const;

private:
    int id_;                                        // Database ID (auto-increment)
    std::string medicine_id_;                       // Unique medicine identifier
    std::string name_;                              // Medicine name
    std::string description_;                       // Medicine description
    std::string dosage_;                            // Standard dosage information
    std::vector<std::string> side_effects_;         // List of side effects
    std::vector<std::string> contraindications_;    // List of contraindications
    int stock_quantity_;                            // Current stock quantity
    double unit_price_;                             // Price per unit
    Core::DateTime created_at_;                     // Creation timestamp
    Core::DateTime updated_at_;                     // Last update timestamp

    /**
     * @brief Initialize default values
     */
    void initialize_defaults();
};

} // namespace Entities
} // namespace HospitalSystem
