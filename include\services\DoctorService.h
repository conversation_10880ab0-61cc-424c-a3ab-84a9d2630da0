#pragma once

#include <string>
#include <vector>
#include <memory>
#include <optional>
#include "entities/Doctor.h"
#include "core/Database.h"
#include "core/Logger.h"

namespace HospitalSystem {
namespace Services {

/**
 * @brief Service class for managing doctor operations
 * 
 * This class provides CRUD operations and business logic for doctor management
 * including registration, updates, searches, and schedule management.
 */
class DoctorService {
public:
    /**
     * @brief Result structure for service operations
     */
    struct ServiceResult {
        bool success;
        std::string message;
        int affected_rows;
        
        ServiceResult(bool s = false, const std::string& m = "", int rows = 0)
            : success(s), message(m), affected_rows(rows) {}
    };

    /**
     * @brief Constructor
     * @param database Reference to database instance
     * @param logger Reference to logger instance
     */
    DoctorService(Core::Database& database, Core::Logger& logger);

    /**
     * @brief Destructor
     */
    ~DoctorService() = default;

    // Disable copy constructor and assignment operator
    DoctorService(const DoctorService&) = delete;
    DoctorService& operator=(const DoctorService&) = delete;

    /**
     * @brief Register a new doctor
     * @param doctor Doctor object to register
     * @return ServiceResult with operation status and generated doctor ID
     */
    ServiceResult register_doctor(Entities::Doctor& doctor);

    /**
     * @brief Get doctor by ID
     * @param doctor_id Doctor's unique identifier
     * @return Optional Doctor object (empty if not found)
     */
    std::optional<Entities::Doctor> get_doctor(const std::string& doctor_id);

    /**
     * @brief Get doctor by database ID
     * @param id Database ID
     * @return Optional Doctor object (empty if not found)
     */
    std::optional<Entities::Doctor> get_doctor_by_id(int id);

    /**
     * @brief Update existing doctor information
     * @param doctor Doctor object with updated information
     * @return ServiceResult with operation status
     */
    ServiceResult update_doctor(const Entities::Doctor& doctor);

    /**
     * @brief Delete doctor by ID
     * @param doctor_id Doctor's unique identifier
     * @return ServiceResult with operation status
     */
    ServiceResult delete_doctor(const std::string& doctor_id);

    /**
     * @brief Search doctors by name
     * @param name_query Name or partial name to search for
     * @return Vector of matching doctors
     */
    std::vector<Entities::Doctor> search_doctors_by_name(const std::string& name_query);

    /**
     * @brief Search doctors by specialization
     * @param specialization Specialization to search for
     * @return Vector of matching doctors
     */
    std::vector<Entities::Doctor> search_doctors_by_specialization(const std::string& specialization);

    /**
     * @brief Get all doctors with pagination
     * @param offset Number of records to skip
     * @param limit Maximum number of records to return
     * @return Vector of doctors
     */
    std::vector<Entities::Doctor> get_all_doctors(int offset = 0, int limit = 100);

    /**
     * @brief Get total count of doctors
     * @return Total number of doctors in database
     */
    int get_doctor_count();

    /**
     * @brief Get available doctors for a specific date and time
     * @param date_time DateTime to check availability for
     * @return Vector of available doctors
     */
    std::vector<Entities::Doctor> get_available_doctors(const Core::DateTime& date_time);

    /**
     * @brief Get doctors by specialization
     * @param specialization Specialization to filter by
     * @return Vector of doctors with specified specialization
     */
    std::vector<Entities::Doctor> get_doctors_by_specialization(const std::string& specialization);

    /**
     * @brief Add qualification to doctor
     * @param doctor_id Doctor's unique identifier
     * @param qualification Qualification to add
     * @return ServiceResult with operation status
     */
    ServiceResult add_doctor_qualification(const std::string& doctor_id, const std::string& qualification);

    /**
     * @brief Remove qualification from doctor
     * @param doctor_id Doctor's unique identifier
     * @param qualification Qualification to remove
     * @return ServiceResult with operation status
     */
    ServiceResult remove_doctor_qualification(const std::string& doctor_id, const std::string& qualification);

    /**
     * @brief Set doctor's availability for a specific day
     * @param doctor_id Doctor's unique identifier
     * @param day Day of the week
     * @param time_slot Time slot for availability
     * @return ServiceResult with operation status
     */
    ServiceResult set_doctor_availability(const std::string& doctor_id, 
                                         Entities::Doctor::WeekDay day, 
                                         const Entities::Doctor::TimeSlot& time_slot);

    /**
     * @brief Remove doctor's availability for a specific day
     * @param doctor_id Doctor's unique identifier
     * @param day Day of the week
     * @return ServiceResult with operation status
     */
    ServiceResult remove_doctor_availability(const std::string& doctor_id, Entities::Doctor::WeekDay day);

    /**
     * @brief Check if doctor exists
     * @param doctor_id Doctor's unique identifier
     * @return True if doctor exists, false otherwise
     */
    bool doctor_exists(const std::string& doctor_id);

    /**
     * @brief Validate doctor data before operations
     * @param doctor Doctor object to validate
     * @return ServiceResult with validation status
     */
    ServiceResult validate_doctor(const Entities::Doctor& doctor);

    /**
     * @brief Get doctor's schedule for a specific week
     * @param doctor_id Doctor's unique identifier
     * @param week_start_date Start date of the week
     * @return JSON object with doctor's weekly schedule
     */
    nlohmann::json get_doctor_weekly_schedule(const std::string& doctor_id, 
                                             const Core::DateTime& week_start_date);

    /**
     * @brief Get all unique specializations
     * @return Vector of all specializations in the system
     */
    std::vector<std::string> get_all_specializations();

    /**
     * @brief Export doctor data to JSON
     * @param doctor_id Doctor's unique identifier
     * @return JSON string representation of doctor data
     */
    std::string export_doctor_data(const std::string& doctor_id);

    /**
     * @brief Import doctor data from JSON
     * @param json_data JSON string containing doctor data
     * @return ServiceResult with import status
     */
    ServiceResult import_doctor_data(const std::string& json_data);

    /**
     * @brief Get doctor statistics
     * @return JSON object with various doctor statistics
     */
    nlohmann::json get_doctor_statistics();

    /**
     * @brief Check doctor availability for appointment
     * @param doctor_id Doctor's unique identifier
     * @param appointment_datetime Proposed appointment date and time
     * @return True if doctor is available, false otherwise
     */
    bool is_doctor_available_for_appointment(const std::string& doctor_id, 
                                           const Core::DateTime& appointment_datetime);

private:
    Core::Database& database_;
    Core::Logger& logger_;

    /**
     * @brief Convert database row to Doctor object
     * @param row Database row data
     * @return Doctor object created from row data
     */
    Entities::Doctor row_to_doctor(const Core::Database::Row& row);

    /**
     * @brief Generate unique doctor ID
     * @return Unique doctor identifier
     */
    std::string generate_doctor_id();

    /**
     * @brief Log service operation
     * @param operation Operation name
     * @param doctor_id Doctor ID involved
     * @param success Operation success status
     * @param message Additional message
     */
    void log_operation(const std::string& operation, const std::string& doctor_id, 
                      bool success, const std::string& message = "");

    /**
     * @brief Save doctor's schedule to database
     * @param doctor Doctor object with schedule
     * @return True if successful, false otherwise
     */
    bool save_doctor_schedule(const Entities::Doctor& doctor);

    /**
     * @brief Load doctor's schedule from database
     * @param doctor Doctor object to load schedule into
     * @return True if successful, false otherwise
     */
    bool load_doctor_schedule(Entities::Doctor& doctor);
};

} // namespace Services
} // namespace HospitalSystem
