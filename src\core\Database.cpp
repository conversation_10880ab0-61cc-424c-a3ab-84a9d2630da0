#include "core/Database.h"
#include <iostream>
#include <sstream>

namespace HospitalSystem {
namespace Core {

Database::Database(const std::string& db_path) 
    : db_(nullptr), db_path_(db_path), is_open_(false) {
}

Database::~Database() {
    close();
}

Database::Database(Database&& other) noexcept 
    : db_(other.db_), db_path_(std::move(other.db_path_)), is_open_(other.is_open_) {
    other.db_ = nullptr;
    other.is_open_ = false;
}

Database& Database::operator=(Database&& other) noexcept {
    if (this != &other) {
        close();
        db_ = other.db_;
        db_path_ = std::move(other.db_path_);
        is_open_ = other.is_open_;
        other.db_ = nullptr;
        other.is_open_ = false;
    }
    return *this;
}

bool Database::open() {
    if (is_open_) {
        return true;
    }

    int result = sqlite3_open(db_path_.c_str(), &db_);
    if (result != SQLITE_OK) {
        std::cerr << "Cannot open database: " << sqlite3_errmsg(db_) << std::endl;
        sqlite3_close(db_);
        db_ = nullptr;
        return false;
    }

    is_open_ = true;
    
    // Enable foreign key constraints
    execute("PRAGMA foreign_keys = ON;");
    
    return true;
}

void Database::close() {
    if (db_) {
        sqlite3_close(db_);
        db_ = nullptr;
        is_open_ = false;
    }
}

bool Database::is_open() const {
    return is_open_;
}

Database::QueryResult Database::execute(const std::string& query) {
    QueryResult result;
    result.success = false;
    result.affected_rows = 0;
    result.last_insert_id = 0;

    if (!is_open_) {
        result.error_message = "Database is not open";
        return result;
    }

    char* error_msg = nullptr;
    int rc = sqlite3_exec(db_, query.c_str(), nullptr, nullptr, &error_msg);

    if (rc != SQLITE_OK) {
        result.error_message = error_msg ? error_msg : "Unknown error";
        sqlite3_free(error_msg);
        return result;
    }

    result.success = true;
    result.affected_rows = sqlite3_changes(db_);
    result.last_insert_id = sqlite3_last_insert_rowid(db_);

    return result;
}

std::pair<Database::QueryResult, Database::ResultSet> Database::select(const std::string& query) {
    QueryResult result;
    ResultSet resultSet;
    
    result.success = false;
    result.affected_rows = 0;
    result.last_insert_id = 0;

    if (!is_open_) {
        result.error_message = "Database is not open";
        return {result, resultSet};
    }

    sqlite3_stmt* stmt = prepare_statement(query);
    if (!stmt) {
        result.error_message = get_last_error();
        return {result, resultSet};
    }

    auto stmt_result = execute_statement(stmt);
    sqlite3_finalize(stmt);

    return stmt_result;
}

Database::QueryResult Database::execute_prepared(const std::string& query, 
                                                const std::vector<std::string>& params) {
    QueryResult result;
    result.success = false;
    result.affected_rows = 0;
    result.last_insert_id = 0;

    if (!is_open_) {
        result.error_message = "Database is not open";
        return result;
    }

    sqlite3_stmt* stmt = prepare_statement(query);
    if (!stmt) {
        result.error_message = get_last_error();
        return result;
    }

    if (!bind_parameters(stmt, params)) {
        result.error_message = get_last_error();
        sqlite3_finalize(stmt);
        return result;
    }

    int rc = sqlite3_step(stmt);
    if (rc != SQLITE_DONE && rc != SQLITE_ROW) {
        result.error_message = get_last_error();
        sqlite3_finalize(stmt);
        return result;
    }

    result.success = true;
    result.affected_rows = sqlite3_changes(db_);
    result.last_insert_id = sqlite3_last_insert_rowid(db_);

    sqlite3_finalize(stmt);
    return result;
}

std::pair<Database::QueryResult, Database::ResultSet> Database::select_prepared(
    const std::string& query, const std::vector<std::string>& params) {
    
    QueryResult result;
    ResultSet resultSet;
    
    result.success = false;
    result.affected_rows = 0;
    result.last_insert_id = 0;

    if (!is_open_) {
        result.error_message = "Database is not open";
        return {result, resultSet};
    }

    sqlite3_stmt* stmt = prepare_statement(query);
    if (!stmt) {
        result.error_message = get_last_error();
        return {result, resultSet};
    }

    if (!bind_parameters(stmt, params)) {
        result.error_message = get_last_error();
        sqlite3_finalize(stmt);
        return {result, resultSet};
    }

    auto stmt_result = execute_statement(stmt);
    sqlite3_finalize(stmt);

    return stmt_result;
}

bool Database::begin_transaction() {
    auto result = execute("BEGIN TRANSACTION;");
    return result.success;
}

bool Database::commit_transaction() {
    auto result = execute("COMMIT;");
    return result.success;
}

bool Database::rollback_transaction() {
    auto result = execute("ROLLBACK;");
    return result.success;
}

bool Database::execute_transaction(const std::vector<std::string>& statements) {
    if (!begin_transaction()) {
        return false;
    }

    for (const auto& statement : statements) {
        auto result = execute(statement);
        if (!result.success) {
            rollback_transaction();
            return false;
        }
    }

    return commit_transaction();
}

std::string Database::get_last_error() const {
    if (db_) {
        return sqlite3_errmsg(db_);
    }
    return "Database not initialized";
}

std::string Database::escape_string(const std::string& input) {
    std::string escaped;
    escaped.reserve(input.length() * 2);
    
    for (char c : input) {
        if (c == '\'') {
            escaped += "''";
        } else {
            escaped += c;
        }
    }
    
    return escaped;
}

sqlite3_stmt* Database::prepare_statement(const std::string& query) {
    sqlite3_stmt* stmt = nullptr;
    int rc = sqlite3_prepare_v2(db_, query.c_str(), -1, &stmt, nullptr);
    
    if (rc != SQLITE_OK) {
        return nullptr;
    }
    
    return stmt;
}

bool Database::bind_parameters(sqlite3_stmt* stmt, const std::vector<std::string>& params) {
    for (size_t i = 0; i < params.size(); ++i) {
        int rc = sqlite3_bind_text(stmt, static_cast<int>(i + 1), params[i].c_str(), -1, SQLITE_STATIC);
        if (rc != SQLITE_OK) {
            return false;
        }
    }
    return true;
}

bool Database::initialize_schema() {
    std::vector<std::string> schema_statements = {
        R"(CREATE TABLE IF NOT EXISTS patients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            patient_id TEXT UNIQUE NOT NULL,
            name TEXT NOT NULL,
            age INTEGER NOT NULL,
            gender TEXT NOT NULL,
            phone TEXT,
            email TEXT,
            address TEXT,
            medical_history TEXT,
            allergies TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        );)",

        R"(CREATE TABLE IF NOT EXISTS doctors (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            doctor_id TEXT UNIQUE NOT NULL,
            name TEXT NOT NULL,
            specialization TEXT NOT NULL,
            phone TEXT,
            email TEXT,
            qualifications TEXT,
            schedule TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        );)",

        R"(CREATE TABLE IF NOT EXISTS appointments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            appointment_id TEXT UNIQUE NOT NULL,
            patient_id TEXT NOT NULL,
            doctor_id TEXT NOT NULL,
            appointment_date TEXT NOT NULL,
            appointment_time TEXT NOT NULL,
            status TEXT NOT NULL DEFAULT 'SCHEDULED',
            notes TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (patient_id) REFERENCES patients(patient_id),
            FOREIGN KEY (doctor_id) REFERENCES doctors(doctor_id)
        );)",

        R"(CREATE TABLE IF NOT EXISTS medicines (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            medicine_id TEXT UNIQUE NOT NULL,
            name TEXT NOT NULL,
            description TEXT,
            dosage TEXT,
            side_effects TEXT,
            contraindications TEXT,
            stock_quantity INTEGER DEFAULT 0,
            unit_price REAL DEFAULT 0.0,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        );)",

        R"(CREATE TABLE IF NOT EXISTS prescriptions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            prescription_id TEXT UNIQUE NOT NULL,
            appointment_id TEXT NOT NULL,
            medicine_id TEXT NOT NULL,
            quantity INTEGER NOT NULL,
            dosage_instructions TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (appointment_id) REFERENCES appointments(appointment_id),
            FOREIGN KEY (medicine_id) REFERENCES medicines(medicine_id)
        );)",

        R"(CREATE TABLE IF NOT EXISTS bills (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            bill_id TEXT UNIQUE NOT NULL,
            patient_id TEXT NOT NULL,
            appointment_id TEXT,
            total_amount REAL NOT NULL DEFAULT 0.0,
            paid_amount REAL NOT NULL DEFAULT 0.0,
            status TEXT NOT NULL DEFAULT 'PENDING',
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (patient_id) REFERENCES patients(patient_id),
            FOREIGN KEY (appointment_id) REFERENCES appointments(appointment_id)
        );)"
    };

    return execute_transaction(schema_statements);
}

std::pair<Database::QueryResult, Database::ResultSet> Database::execute_statement(sqlite3_stmt* stmt) {
    QueryResult result;
    ResultSet resultSet;

    result.success = false;
    result.affected_rows = 0;
    result.last_insert_id = 0;

    int column_count = sqlite3_column_count(stmt);

    while (true) {
        int rc = sqlite3_step(stmt);

        if (rc == SQLITE_ROW) {
            Row row;
            for (int i = 0; i < column_count; ++i) {
                const char* value = reinterpret_cast<const char*>(sqlite3_column_text(stmt, i));
                row.push_back(value ? value : "");
            }
            resultSet.push_back(row);
        } else if (rc == SQLITE_DONE) {
            break;
        } else {
            result.error_message = get_last_error();
            return {result, resultSet};
        }
    }

    result.success = true;
    result.affected_rows = sqlite3_changes(db_);
    result.last_insert_id = sqlite3_last_insert_rowid(db_);

    return {result, resultSet};
}

} // namespace Core
} // namespace HospitalSystem
