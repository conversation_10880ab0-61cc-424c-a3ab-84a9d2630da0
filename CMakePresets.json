{"version": 3, "cmakeMinimumRequired": {"major": 3, "minor": 16, "patch": 0}, "configurePresets": [{"name": "dev", "displayName": "Development Configuration", "description": "Debug build with all symbols and testing enabled", "generator": "Ninja", "binaryDir": "${sourceDir}/build/dev", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "CMAKE_EXPORT_COMPILE_COMMANDS": "ON", "BUILD_TESTS": "ON"}, "environment": {"VCPKG_ROOT": "$env{VCPKG_ROOT}"}}, {"name": "dev-vs", "displayName": "Development Configuration (Visual Studio)", "description": "Debug build for Visual Studio", "generator": "Visual Studio 17 2022", "binaryDir": "${sourceDir}/build/dev-vs", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "BUILD_TESTS": "ON"}}, {"name": "release", "displayName": "Release Configuration", "description": "Optimized release build", "generator": "Ninja", "binaryDir": "${sourceDir}/build/release", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release", "BUILD_TESTS": "OFF"}, "environment": {"VCPKG_ROOT": "$env{VCPKG_ROOT}"}}, {"name": "release-vs", "displayName": "Release Configuration (Visual Studio)", "description": "Optimized release build for Visual Studio", "generator": "Visual Studio 17 2022", "binaryDir": "${sourceDir}/build/release-vs", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release", "BUILD_TESTS": "OFF"}}], "buildPresets": [{"name": "dev-build", "configurePreset": "dev", "displayName": "Build Development Version"}, {"name": "dev-vs-build", "configurePreset": "dev-vs", "displayName": "Build Development Version (Visual Studio)"}, {"name": "release-build", "configurePreset": "release", "displayName": "Build Release Version"}, {"name": "release-vs-build", "configurePreset": "release-vs", "displayName": "Build Release Version (Visual Studio)"}], "testPresets": [{"name": "unit-tests", "configurePreset": "dev", "description": "Run unit tests", "filter": {"include": {"name": "UnitTests"}}}, {"name": "integration-tests", "configurePreset": "dev", "description": "Run integration tests", "filter": {"include": {"name": "IntegrationTests"}}}, {"name": "all-tests", "configurePreset": "dev", "description": "Run all tests"}]}