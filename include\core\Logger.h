#pragma once

#include <string>
#include <fstream>
#include <memory>
#include <mutex>
#include <chrono>
#include <sstream>

namespace HospitalSystem {
namespace Core {

/**
 * @brief Logging levels enumeration
 */
enum class LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARNING = 2,
    ERROR = 3,
    CRITICAL = 4
};

/**
 * @brief Thread-safe logger class for the hospital management system
 * 
 * This class provides logging functionality with different log levels,
 * file output, and thread safety for concurrent operations.
 */
class Logger {
public:
    /**
     * @brief Constructor
     * @param log_file_path Path to the log file (optional, logs to console if empty)
     * @param min_level Minimum log level to record
     */
    explicit Logger(const std::string& log_file_path = "", 
                   LogLevel min_level = LogLevel::INFO);

    /**
     * @brief Destructor - ensures all logs are flushed
     */
    ~Logger();

    // Disable copy constructor and assignment operator
    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;

    // Enable move constructor and assignment operator
    Logger(Logger&& other) noexcept;
    Logger& operator=(Logger&& other) noexcept;

    /**
     * @brief Set minimum log level
     * @param level Minimum level to log
     */
    void set_log_level(LogLevel level);

    /**
     * @brief Get current minimum log level
     * @return Current minimum log level
     */
    LogLevel get_log_level() const;

    /**
     * @brief Log a debug message
     * @param message Message to log
     */
    void debug(const std::string& message);

    /**
     * @brief Log an info message
     * @param message Message to log
     */
    void info(const std::string& message);

    /**
     * @brief Log a warning message
     * @param message Message to log
     */
    void warning(const std::string& message);

    /**
     * @brief Log an error message
     * @param message Message to log
     */
    void error(const std::string& message);

    /**
     * @brief Log a critical message
     * @param message Message to log
     */
    void critical(const std::string& message);

    /**
     * @brief Log a message with specified level
     * @param level Log level
     * @param message Message to log
     */
    void log(LogLevel level, const std::string& message);

    /**
     * @brief Log a formatted message (printf-style)
     * @param level Log level
     * @param format Format string
     * @param args Arguments for formatting
     */
    template<typename... Args>
    void log_formatted(LogLevel level, const std::string& format, Args&&... args);

    /**
     * @brief Flush all pending log messages
     */
    void flush();

    /**
     * @brief Enable or disable console output
     * @param enable True to enable console output, false to disable
     */
    void enable_console_output(bool enable);

    /**
     * @brief Enable or disable timestamp in log messages
     * @param enable True to enable timestamps, false to disable
     */
    void enable_timestamp(bool enable);

    /**
     * @brief Get singleton instance of logger
     * @return Reference to the singleton logger instance
     */
    static Logger& get_instance();

    /**
     * @brief Initialize global logger with file path
     * @param log_file_path Path to log file
     * @param min_level Minimum log level
     */
    static void initialize(const std::string& log_file_path, 
                          LogLevel min_level = LogLevel::INFO);

private:
    std::string log_file_path_;
    std::unique_ptr<std::ofstream> log_file_;
    LogLevel min_level_;
    bool console_output_enabled_;
    bool timestamp_enabled_;
    mutable std::mutex mutex_;

    static std::unique_ptr<Logger> instance_;
    static std::mutex instance_mutex_;

    /**
     * @brief Get current timestamp as string
     * @return Formatted timestamp string
     */
    std::string get_timestamp() const;

    /**
     * @brief Convert log level to string
     * @param level Log level to convert
     * @return String representation of log level
     */
    std::string level_to_string(LogLevel level) const;

    /**
     * @brief Write log message to outputs
     * @param level Log level
     * @param message Message to write
     */
    void write_log(LogLevel level, const std::string& message);

    /**
     * @brief Open log file for writing
     * @return True if successful, false otherwise
     */
    bool open_log_file();
};

// Template implementation
template<typename... Args>
void Logger::log_formatted(LogLevel level, const std::string& format, Args&&... args) {
    if (level < min_level_) {
        return;
    }

    std::ostringstream oss;
    format_string(oss, format, std::forward<Args>(args)...);
    log(level, oss.str());
}

// Helper function for string formatting
template<typename T>
void format_string(std::ostringstream& oss, const std::string& format, T&& value) {
    size_t pos = format.find("{}");
    if (pos != std::string::npos) {
        oss << format.substr(0, pos) << std::forward<T>(value) << format.substr(pos + 2);
    } else {
        oss << format;
    }
}

template<typename T, typename... Args>
void format_string(std::ostringstream& oss, const std::string& format, T&& value, Args&&... args) {
    size_t pos = format.find("{}");
    if (pos != std::string::npos) {
        std::string new_format = format.substr(0, pos) + std::to_string(value) + format.substr(pos + 2);
        format_string(oss, new_format, std::forward<Args>(args)...);
    } else {
        oss << format;
    }
}

} // namespace Core
} // namespace HospitalSystem
