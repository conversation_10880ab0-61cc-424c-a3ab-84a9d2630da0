#include <catch2/catch_test_macros.hpp>
#include "core/Utils.h"

using namespace HospitalSystem::Core;

TEST_CASE("DateTime Basic Operations", "[datetime]") {
    SECTION("Default constructor creates current time") {
        DateTime dt1 = DateTime::now();
        DateTime dt2 = DateTime::now();
        
        // Should be very close in time (within a few seconds)
        REQUIRE(dt1.days_difference(dt2) == 0);
    }
    
    SECTION("Constructor with string creates correct date") {
        DateTime dt("2024-01-15 10:30:45");
        
        REQUIRE(dt.to_date_string() == "2024-01-15");
        REQUIRE(dt.to_time_string() == "10:30:45");
        REQUIRE(dt.to_string() == "2024-01-15 10:30:45");
    }
    
    SECTION("Date-only constructor works") {
        DateTime dt("2024-01-15");
        
        REQUIRE(dt.to_date_string() == "2024-01-15");
        REQUIRE(dt.to_time_string() == "00:00:00");
    }
}

TEST_CASE("DateTime Comparison Operations", "[datetime]") {
    DateTime dt1("2024-01-15 10:30:45");
    DateTime dt2("2024-01-15 10:30:45");
    DateTime dt3("2024-01-16 10:30:45");
    DateTime dt4("2024-01-14 10:30:45");
    
    SECTION("Equality comparison works") {
        REQUIRE(dt1 == dt2);
        REQUIRE_FALSE(dt1 == dt3);
    }
    
    SECTION("Inequality comparison works") {
        REQUIRE_FALSE(dt1 != dt2);
        REQUIRE(dt1 != dt3);
    }
    
    SECTION("Less than comparison works") {
        REQUIRE(dt4 < dt1);
        REQUIRE(dt1 < dt3);
        REQUIRE_FALSE(dt1 < dt2);
        REQUIRE_FALSE(dt3 < dt1);
    }
    
    SECTION("is_before method works") {
        REQUIRE(dt4.is_before(dt1));
        REQUIRE(dt1.is_before(dt3));
        REQUIRE_FALSE(dt1.is_before(dt2));
        REQUIRE_FALSE(dt3.is_before(dt1));
    }
    
    SECTION("is_after method works") {
        REQUIRE(dt1.is_after(dt4));
        REQUIRE(dt3.is_after(dt1));
        REQUIRE_FALSE(dt2.is_after(dt1));
        REQUIRE_FALSE(dt1.is_after(dt3));
    }
}

TEST_CASE("DateTime Arithmetic Operations", "[datetime]") {
    DateTime dt("2024-01-15 10:30:45");
    
    SECTION("Adding days works") {
        DateTime result = dt.add_days(5);
        REQUIRE(result.to_date_string() == "2024-01-20");
        REQUIRE(result.to_time_string() == "10:30:45");
    }
    
    SECTION("Adding hours works") {
        DateTime result = dt.add_hours(25); // Should roll over to next day
        REQUIRE(result.to_date_string() == "2024-01-16");
        REQUIRE(result.to_time_string() == "11:30:45");
    }
    
    SECTION("Adding minutes works") {
        DateTime result = dt.add_minutes(90); // 1.5 hours
        REQUIRE(result.to_time_string() == "12:00:45");
    }
    
    SECTION("Days difference calculation works") {
        DateTime dt1("2024-01-15");
        DateTime dt2("2024-01-20");
        DateTime dt3("2024-01-10");
        
        REQUIRE(dt2.days_difference(dt1) == 5);
        REQUIRE(dt1.days_difference(dt2) == -5);
        REQUIRE(dt1.days_difference(dt3) == 5);
    }
}

TEST_CASE("StringUtils Operations", "[stringutils]") {
    SECTION("to_upper converts to uppercase") {
        REQUIRE(StringUtils::to_upper("hello") == "HELLO");
        REQUIRE(StringUtils::to_upper("Hello World") == "HELLO WORLD");
        REQUIRE(StringUtils::to_upper("") == "");
    }
    
    SECTION("to_lower converts to lowercase") {
        REQUIRE(StringUtils::to_lower("HELLO") == "hello");
        REQUIRE(StringUtils::to_lower("Hello World") == "hello world");
        REQUIRE(StringUtils::to_lower("") == "");
    }
    
    SECTION("trim removes whitespace") {
        REQUIRE(StringUtils::trim("  hello  ") == "hello");
        REQUIRE(StringUtils::trim("\t\nhello\r\n\t") == "hello");
        REQUIRE(StringUtils::trim("hello") == "hello");
        REQUIRE(StringUtils::trim("   ") == "");
    }
    
    SECTION("split works with different delimiters") {
        auto result = StringUtils::split("a,b,c", ",");
        REQUIRE(result.size() == 3);
        REQUIRE(result[0] == "a");
        REQUIRE(result[1] == "b");
        REQUIRE(result[2] == "c");
        
        auto result2 = StringUtils::split("one two three", " ");
        REQUIRE(result2.size() == 3);
        REQUIRE(result2[0] == "one");
        REQUIRE(result2[1] == "two");
        REQUIRE(result2[2] == "three");
    }
    
    SECTION("join combines strings") {
        std::vector<std::string> parts = {"a", "b", "c"};
        REQUIRE(StringUtils::join(parts, ",") == "a,b,c");
        REQUIRE(StringUtils::join(parts, " - ") == "a - b - c");
        
        std::vector<std::string> empty_parts;
        REQUIRE(StringUtils::join(empty_parts, ",") == "");
    }
    
    SECTION("starts_with works correctly") {
        REQUIRE(StringUtils::starts_with("hello world", "hello"));
        REQUIRE(StringUtils::starts_with("hello", "hello"));
        REQUIRE_FALSE(StringUtils::starts_with("hello", "world"));
        REQUIRE_FALSE(StringUtils::starts_with("hi", "hello"));
    }
    
    SECTION("ends_with works correctly") {
        REQUIRE(StringUtils::ends_with("hello world", "world"));
        REQUIRE(StringUtils::ends_with("world", "world"));
        REQUIRE_FALSE(StringUtils::ends_with("hello world", "hello"));
        REQUIRE_FALSE(StringUtils::ends_with("hi", "hello"));
    }
}

TEST_CASE("StringUtils Validation", "[stringutils]") {
    SECTION("is_valid_email validates email addresses") {
        REQUIRE(StringUtils::is_valid_email("<EMAIL>"));
        REQUIRE(StringUtils::is_valid_email("<EMAIL>"));
        REQUIRE(StringUtils::is_valid_email("<EMAIL>"));
        
        REQUIRE_FALSE(StringUtils::is_valid_email("invalid.email"));
        REQUIRE_FALSE(StringUtils::is_valid_email("@example.com"));
        REQUIRE_FALSE(StringUtils::is_valid_email("user@"));
        REQUIRE_FALSE(StringUtils::is_valid_email(""));
    }
    
    SECTION("is_valid_phone validates phone numbers") {
        REQUIRE(StringUtils::is_valid_phone("************"));
        REQUIRE(StringUtils::is_valid_phone("(*************"));
        REQUIRE(StringUtils::is_valid_phone("******-456-7890"));
        REQUIRE(StringUtils::is_valid_phone("1234567890"));
        
        REQUIRE_FALSE(StringUtils::is_valid_phone("123"));
        REQUIRE_FALSE(StringUtils::is_valid_phone("abc-def-ghij"));
        REQUIRE_FALSE(StringUtils::is_valid_phone(""));
    }
}

TEST_CASE("ValidationUtils Operations", "[validationutils]") {
    SECTION("is_valid_name validates names") {
        REQUIRE(ValidationUtils::is_valid_name("John Doe"));
        REQUIRE(ValidationUtils::is_valid_name("Mary Jane Smith"));
        REQUIRE(ValidationUtils::is_valid_name("O'Connor"));
        REQUIRE(ValidationUtils::is_valid_name("Jean-Pierre"));
        
        REQUIRE_FALSE(ValidationUtils::is_valid_name(""));
        REQUIRE_FALSE(ValidationUtils::is_valid_name("J"));
        REQUIRE_FALSE(ValidationUtils::is_valid_name("John123"));
        REQUIRE_FALSE(ValidationUtils::is_valid_name("John@Doe"));
    }
    
    SECTION("is_valid_age validates age ranges") {
        REQUIRE(ValidationUtils::is_valid_age(0));
        REQUIRE(ValidationUtils::is_valid_age(25));
        REQUIRE(ValidationUtils::is_valid_age(100));
        REQUIRE(ValidationUtils::is_valid_age(150));
        
        REQUIRE_FALSE(ValidationUtils::is_valid_age(-1));
        REQUIRE_FALSE(ValidationUtils::is_valid_age(151));
        REQUIRE_FALSE(ValidationUtils::is_valid_age(200));
    }
    
    SECTION("is_valid_gender validates gender values") {
        REQUIRE(ValidationUtils::is_valid_gender("Male"));
        REQUIRE(ValidationUtils::is_valid_gender("Female"));
        REQUIRE(ValidationUtils::is_valid_gender("Other"));
        REQUIRE(ValidationUtils::is_valid_gender("male"));
        REQUIRE(ValidationUtils::is_valid_gender("FEMALE"));
        
        REQUIRE_FALSE(ValidationUtils::is_valid_gender(""));
        REQUIRE_FALSE(ValidationUtils::is_valid_gender("Unknown"));
        REQUIRE_FALSE(ValidationUtils::is_valid_gender("M"));
    }
    
    SECTION("is_valid_appointment_time validates appointment times") {
        // Future date during working hours
        DateTime future_working("2025-06-25 10:00:00");
        REQUIRE(ValidationUtils::is_valid_appointment_time(future_working));
        
        // Past date should be invalid
        DateTime past("2023-01-01 10:00:00");
        REQUIRE_FALSE(ValidationUtils::is_valid_appointment_time(past));
        
        // Future date but outside working hours
        DateTime future_late("2025-06-25 22:00:00");
        REQUIRE_FALSE(ValidationUtils::is_valid_appointment_time(future_late));
        
        DateTime future_early("2025-06-25 06:00:00");
        REQUIRE_FALSE(ValidationUtils::is_valid_appointment_time(future_early));
    }
}

TEST_CASE("IdGenerator Operations", "[idgenerator]") {
    SECTION("generate_id creates unique IDs") {
        std::string id1 = IdGenerator::generate_id("PAT", 8);
        std::string id2 = IdGenerator::generate_id("PAT", 8);
        
        REQUIRE(id1.length() == 11); // PAT + 8 characters
        REQUIRE(id2.length() == 11);
        REQUIRE(id1 != id2); // Should be unique
        REQUIRE(id1.substr(0, 3) == "PAT");
        REQUIRE(id2.substr(0, 3) == "PAT");
    }
    
    SECTION("generate_patient_id creates valid patient IDs") {
        std::string id = IdGenerator::generate_patient_id();
        
        REQUIRE(id.length() == 11); // PAT + 8 characters
        REQUIRE(id.substr(0, 3) == "PAT");
    }
    
    SECTION("generate_doctor_id creates valid doctor IDs") {
        std::string id = IdGenerator::generate_doctor_id();
        
        REQUIRE(id.length() == 11); // DOC + 8 characters
        REQUIRE(id.substr(0, 3) == "DOC");
    }
    
    SECTION("generate_appointment_id creates valid appointment IDs") {
        std::string id = IdGenerator::generate_appointment_id();
        
        REQUIRE(id.length() == 13); // APT + 10 characters
        REQUIRE(id.substr(0, 3) == "APT");
    }
}
