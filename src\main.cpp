#include <iostream>
#include <string>
#include <memory>
#include <vector>
#include <iomanip>

#include "core/Database.h"
#include "core/Logger.h"
#include "core/Utils.h"
#include "entities/Patient.h"
#include "entities/Doctor.h"
#include "entities/Appointment.h"
#include "entities/Medicine.h"
#include "services/PatientService.h"
#include "services/DoctorService.h"
#include "services/BillingService.h"

using namespace HospitalSystem;

/**
 * @brief Hospital Management System Console Application
 * 
 * This is the main console interface for the Hospital Management System.
 * It provides a menu-driven interface for managing patients, doctors,
 * appointments, and billing operations.
 */
class HospitalManagementApp {
public:
    HospitalManagementApp() 
        : database_("hospital.db"),
          logger_(Core::Logger::get_instance()),
          patient_service_(database_, logger_),
          doctor_service_(database_, logger_),
          billing_service_(database_, logger_) {
        
        // Initialize database
        if (!database_.initialize()) {
            logger_.log(Core::Logger::Level::CRITICAL, "Failed to initialize database");
            throw std::runtime_error("Database initialization failed");
        }
        
        // Initialize database schema
        if (!database_.initialize_schema()) {
            logger_.log(Core::Logger::Level::CRITICAL, "Failed to initialize database schema");
            throw std::runtime_error("Database schema initialization failed");
        }
        
        logger_.log(Core::Logger::Level::INFO, "Hospital Management System initialized successfully");
    }

    void run() {
        display_welcome();
        
        while (true) {
            display_main_menu();
            int choice = get_user_choice();
            
            try {
                switch (choice) {
                    case 1:
                        handle_patient_management();
                        break;
                    case 2:
                        handle_doctor_management();
                        break;
                    case 3:
                        handle_appointment_management();
                        break;
                    case 4:
                        handle_billing_management();
                        break;
                    case 5:
                        handle_reports();
                        break;
                    case 6:
                        handle_system_settings();
                        break;
                    case 0:
                        std::cout << "\nThank you for using Hospital Management System!\n";
                        logger_.log(Core::Logger::Level::INFO, "Application terminated by user");
                        return;
                    default:
                        std::cout << "\nInvalid choice. Please try again.\n";
                        break;
                }
            } catch (const std::exception& e) {
                std::cout << "\nError: " << e.what() << "\n";
                logger_.log(Core::Logger::Level::ERROR, "Exception in main menu: " + std::string(e.what()));
            }
            
            pause_for_user();
        }
    }

private:
    Core::Database database_;
    Core::Logger& logger_;
    Services::PatientService patient_service_;
    Services::DoctorService doctor_service_;
    Services::BillingService billing_service_;

    void display_welcome() {
        std::cout << "\n";
        std::cout << "========================================\n";
        std::cout << "    HOSPITAL MANAGEMENT SYSTEM\n";
        std::cout << "========================================\n";
        std::cout << "Welcome to the Hospital Management System\n";
        std::cout << "Version 1.0 - Built with C++17\n";
        std::cout << "========================================\n\n";
    }

    void display_main_menu() {
        std::cout << "\n=== MAIN MENU ===\n";
        std::cout << "1. Patient Management\n";
        std::cout << "2. Doctor Management\n";
        std::cout << "3. Appointment Management\n";
        std::cout << "4. Billing Management\n";
        std::cout << "5. Reports\n";
        std::cout << "6. System Settings\n";
        std::cout << "0. Exit\n";
        std::cout << "==================\n";
        std::cout << "Enter your choice: ";
    }

    void handle_patient_management() {
        while (true) {
            std::cout << "\n=== PATIENT MANAGEMENT ===\n";
            std::cout << "1. Register New Patient\n";
            std::cout << "2. View Patient Details\n";
            std::cout << "3. Update Patient Information\n";
            std::cout << "4. Search Patients\n";
            std::cout << "5. List All Patients\n";
            std::cout << "6. Delete Patient\n";
            std::cout << "0. Back to Main Menu\n";
            std::cout << "==========================\n";
            std::cout << "Enter your choice: ";
            
            int choice = get_user_choice();
            
            switch (choice) {
                case 1:
                    register_new_patient();
                    break;
                case 2:
                    view_patient_details();
                    break;
                case 3:
                    update_patient_information();
                    break;
                case 4:
                    search_patients();
                    break;
                case 5:
                    list_all_patients();
                    break;
                case 6:
                    delete_patient();
                    break;
                case 0:
                    return;
                default:
                    std::cout << "\nInvalid choice. Please try again.\n";
                    break;
            }
        }
    }

    void handle_doctor_management() {
        while (true) {
            std::cout << "\n=== DOCTOR MANAGEMENT ===\n";
            std::cout << "1. Register New Doctor\n";
            std::cout << "2. View Doctor Details\n";
            std::cout << "3. Update Doctor Information\n";
            std::cout << "4. Search Doctors\n";
            std::cout << "5. List All Doctors\n";
            std::cout << "6. Manage Doctor Schedule\n";
            std::cout << "7. Delete Doctor\n";
            std::cout << "0. Back to Main Menu\n";
            std::cout << "=========================\n";
            std::cout << "Enter your choice: ";
            
            int choice = get_user_choice();
            
            switch (choice) {
                case 1:
                    register_new_doctor();
                    break;
                case 2:
                    view_doctor_details();
                    break;
                case 3:
                    update_doctor_information();
                    break;
                case 4:
                    search_doctors();
                    break;
                case 5:
                    list_all_doctors();
                    break;
                case 6:
                    manage_doctor_schedule();
                    break;
                case 7:
                    delete_doctor();
                    break;
                case 0:
                    return;
                default:
                    std::cout << "\nInvalid choice. Please try again.\n";
                    break;
            }
        }
    }

    void handle_appointment_management() {
        std::cout << "\n=== APPOINTMENT MANAGEMENT ===\n";
        std::cout << "This feature will be implemented in the next phase.\n";
        std::cout << "===============================\n";
    }

    void handle_billing_management() {
        std::cout << "\n=== BILLING MANAGEMENT ===\n";
        std::cout << "This feature will be implemented in the next phase.\n";
        std::cout << "===========================\n";
    }

    void handle_reports() {
        std::cout << "\n=== REPORTS ===\n";
        std::cout << "1. Patient Statistics\n";
        std::cout << "2. Doctor Statistics\n";
        std::cout << "3. System Overview\n";
        std::cout << "0. Back to Main Menu\n";
        std::cout << "===============\n";
        std::cout << "Enter your choice: ";
        
        int choice = get_user_choice();
        
        switch (choice) {
            case 1:
                show_patient_statistics();
                break;
            case 2:
                show_doctor_statistics();
                break;
            case 3:
                show_system_overview();
                break;
            case 0:
                return;
            default:
                std::cout << "\nInvalid choice. Please try again.\n";
                break;
        }
    }

    void handle_system_settings() {
        std::cout << "\n=== SYSTEM SETTINGS ===\n";
        std::cout << "1. Database Information\n";
        std::cout << "2. Log Settings\n";
        std::cout << "3. Backup Database\n";
        std::cout << "0. Back to Main Menu\n";
        std::cout << "=======================\n";
        std::cout << "Enter your choice: ";
        
        int choice = get_user_choice();
        
        switch (choice) {
            case 1:
                show_database_info();
                break;
            case 2:
                show_log_settings();
                break;
            case 3:
                backup_database();
                break;
            case 0:
                return;
            default:
                std::cout << "\nInvalid choice. Please try again.\n";
                break;
        }
    }

    // Patient Management Functions
    void register_new_patient() {
        std::cout << "\n=== REGISTER NEW PATIENT ===\n";
        
        std::string name, gender, phone, email, address;
        int age;
        
        std::cout << "Enter patient name: ";
        std::cin.ignore();
        std::getline(std::cin, name);
        
        std::cout << "Enter age: ";
        std::cin >> age;
        
        std::cout << "Enter gender (Male/Female/Other): ";
        std::cin >> gender;
        
        std::cout << "Enter phone number: ";
        std::cin >> phone;
        
        std::cout << "Enter email: ";
        std::cin >> email;
        
        std::cout << "Enter address: ";
        std::cin.ignore();
        std::getline(std::cin, address);
        
        // Create patient object
        Entities::Patient patient("", name, age, gender, phone, email, address);
        
        // Register patient
        auto result = patient_service_.register_patient(patient);
        
        if (result.success) {
            std::cout << "\nPatient registered successfully!\n";
            std::cout << "Patient ID: " << patient.get_patient_id() << "\n";
        } else {
            std::cout << "\nFailed to register patient: " << result.message << "\n";
        }
    }

    void view_patient_details() {
        std::cout << "\n=== VIEW PATIENT DETAILS ===\n";
        std::cout << "Enter Patient ID: ";
        
        std::string patient_id;
        std::cin >> patient_id;
        
        auto patient = patient_service_.get_patient(patient_id);
        
        if (patient.has_value()) {
            std::cout << "\n" << patient->to_string() << "\n";
        } else {
            std::cout << "\nPatient not found.\n";
        }
    }

    void update_patient_information() {
        std::cout << "\n=== UPDATE PATIENT INFORMATION ===\n";
        std::cout << "This feature will be implemented in the next phase.\n";
    }

    void search_patients() {
        std::cout << "\n=== SEARCH PATIENTS ===\n";
        std::cout << "1. Search by Name\n";
        std::cout << "2. Search by Phone\n";
        std::cout << "3. Search by Email\n";
        std::cout << "Enter your choice: ";
        
        int choice = get_user_choice();
        std::string query;
        
        switch (choice) {
            case 1:
                std::cout << "Enter name to search: ";
                std::cin.ignore();
                std::getline(std::cin, query);
                display_patient_list(patient_service_.search_patients_by_name(query));
                break;
            case 2:
                std::cout << "Enter phone to search: ";
                std::cin >> query;
                display_patient_list(patient_service_.search_patients_by_phone(query));
                break;
            case 3:
                std::cout << "Enter email to search: ";
                std::cin >> query;
                display_patient_list(patient_service_.search_patients_by_email(query));
                break;
            default:
                std::cout << "\nInvalid choice.\n";
                break;
        }
    }

    void list_all_patients() {
        std::cout << "\n=== ALL PATIENTS ===\n";
        auto patients = patient_service_.get_all_patients(0, 50); // Limit to 50 for display
        display_patient_list(patients);
    }

    void delete_patient() {
        std::cout << "\n=== DELETE PATIENT ===\n";
        std::cout << "Enter Patient ID to delete: ";
        
        std::string patient_id;
        std::cin >> patient_id;
        
        std::cout << "Are you sure you want to delete this patient? (y/N): ";
        char confirm;
        std::cin >> confirm;
        
        if (confirm == 'y' || confirm == 'Y') {
            auto result = patient_service_.delete_patient(patient_id);
            
            if (result.success) {
                std::cout << "\nPatient deleted successfully.\n";
            } else {
                std::cout << "\nFailed to delete patient: " << result.message << "\n";
            }
        } else {
            std::cout << "\nOperation cancelled.\n";
        }
    }

    // Doctor Management Functions
    void register_new_doctor() {
        std::cout << "\n=== REGISTER NEW DOCTOR ===\n";
        std::cout << "This feature will be implemented in the next phase.\n";
    }

    void view_doctor_details() {
        std::cout << "\n=== VIEW DOCTOR DETAILS ===\n";
        std::cout << "This feature will be implemented in the next phase.\n";
    }

    void update_doctor_information() {
        std::cout << "\n=== UPDATE DOCTOR INFORMATION ===\n";
        std::cout << "This feature will be implemented in the next phase.\n";
    }

    void search_doctors() {
        std::cout << "\n=== SEARCH DOCTORS ===\n";
        std::cout << "This feature will be implemented in the next phase.\n";
    }

    void list_all_doctors() {
        std::cout << "\n=== ALL DOCTORS ===\n";
        std::cout << "This feature will be implemented in the next phase.\n";
    }

    void manage_doctor_schedule() {
        std::cout << "\n=== MANAGE DOCTOR SCHEDULE ===\n";
        std::cout << "This feature will be implemented in the next phase.\n";
    }

    void delete_doctor() {
        std::cout << "\n=== DELETE DOCTOR ===\n";
        std::cout << "This feature will be implemented in the next phase.\n";
    }

    // Report Functions
    void show_patient_statistics() {
        std::cout << "\n=== PATIENT STATISTICS ===\n";
        int total_patients = patient_service_.get_patient_count();
        std::cout << "Total Patients: " << total_patients << "\n";
        
        auto stats = patient_service_.get_patient_statistics();
        std::cout << "Statistics: " << stats.dump(2) << "\n";
    }

    void show_doctor_statistics() {
        std::cout << "\n=== DOCTOR STATISTICS ===\n";
        int total_doctors = doctor_service_.get_doctor_count();
        std::cout << "Total Doctors: " << total_doctors << "\n";
    }

    void show_system_overview() {
        std::cout << "\n=== SYSTEM OVERVIEW ===\n";
        std::cout << "Hospital Management System v1.0\n";
        std::cout << "Database: SQLite\n";
        std::cout << "Build Date: " << __DATE__ << " " << __TIME__ << "\n";
        std::cout << "Current Time: " << Core::DateTime::now().to_string() << "\n";
    }

    // System Settings Functions
    void show_database_info() {
        std::cout << "\n=== DATABASE INFORMATION ===\n";
        std::cout << "Database File: hospital.db\n";
        std::cout << "Database Type: SQLite\n";
        std::cout << "Status: Connected\n";
    }

    void show_log_settings() {
        std::cout << "\n=== LOG SETTINGS ===\n";
        std::cout << "Log Level: INFO\n";
        std::cout << "Log File: hospital.log\n";
        std::cout << "Console Logging: Enabled\n";
    }

    void backup_database() {
        std::cout << "\n=== BACKUP DATABASE ===\n";
        std::cout << "This feature will be implemented in the next phase.\n";
    }

    // Utility Functions
    void display_patient_list(const std::vector<Entities::Patient>& patients) {
        if (patients.empty()) {
            std::cout << "\nNo patients found.\n";
            return;
        }
        
        std::cout << "\n" << std::setw(15) << "Patient ID" 
                  << std::setw(25) << "Name" 
                  << std::setw(5) << "Age" 
                  << std::setw(10) << "Gender" 
                  << std::setw(15) << "Phone" << "\n";
        std::cout << std::string(70, '-') << "\n";
        
        for (const auto& patient : patients) {
            std::cout << std::setw(15) << patient.get_patient_id()
                      << std::setw(25) << patient.get_name()
                      << std::setw(5) << patient.get_age()
                      << std::setw(10) << patient.get_gender()
                      << std::setw(15) << patient.get_phone() << "\n";
        }
    }

    int get_user_choice() {
        int choice;
        std::cin >> choice;
        return choice;
    }

    void pause_for_user() {
        std::cout << "\nPress Enter to continue...";
        std::cin.ignore();
        std::cin.get();
    }
};

int main() {
    try {
        HospitalManagementApp app;
        app.run();
    } catch (const std::exception& e) {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
