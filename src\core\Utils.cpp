#include "core/Utils.h"
#include <ctime>
#include <stdexcept>

namespace HospitalSystem {
namespace Core {

// DateTime implementation
DateTime::DateTime() : time_point_(std::chrono::system_clock::now()) {}

DateTime::DateTime(int year, int month, int day, int hour, int minute, int second) {
    std::tm tm = {};
    tm.tm_year = year - 1900;
    tm.tm_mon = month - 1;
    tm.tm_mday = day;
    tm.tm_hour = hour;
    tm.tm_min = minute;
    tm.tm_sec = second;
    
    std::time_t time = std::mktime(&tm);
    if (time == -1) {
        throw std::invalid_argument("Invalid date/time values");
    }
    
    time_point_ = std::chrono::system_clock::from_time_t(time);
}

DateTime::DateTime(const std::string& datetime_str) {
    std::tm tm = {};
    std::istringstream ss(datetime_str);
    
    // Try to parse YYYY-MM-DD HH:MM:SS format
    ss >> std::get_time(&tm, "%Y-%m-%d %H:%M:%S");
    
    if (ss.fail()) {
        // Try to parse YYYY-MM-DD format
        ss.clear();
        ss.str(datetime_str);
        ss >> std::get_time(&tm, "%Y-%m-%d");
        
        if (ss.fail()) {
            throw std::invalid_argument("Invalid date/time format. Expected YYYY-MM-DD or YYYY-MM-DD HH:MM:SS");
        }
    }
    
    std::time_t time = std::mktime(&tm);
    if (time == -1) {
        throw std::invalid_argument("Invalid date/time values");
    }
    
    time_point_ = std::chrono::system_clock::from_time_t(time);
}

DateTime DateTime::now() {
    return DateTime();
}

std::string DateTime::to_string() const {
    std::time_t time = std::chrono::system_clock::to_time_t(time_point_);
    std::tm* tm = std::localtime(&time);
    
    std::ostringstream oss;
    oss << std::put_time(tm, "%Y-%m-%d %H:%M:%S");
    return oss.str();
}

std::string DateTime::to_date_string() const {
    std::time_t time = std::chrono::system_clock::to_time_t(time_point_);
    std::tm* tm = std::localtime(&time);
    
    std::ostringstream oss;
    oss << std::put_time(tm, "%Y-%m-%d");
    return oss.str();
}

std::string DateTime::to_time_string() const {
    std::time_t time = std::chrono::system_clock::to_time_t(time_point_);
    std::tm* tm = std::localtime(&time);
    
    std::ostringstream oss;
    oss << std::put_time(tm, "%H:%M:%S");
    return oss.str();
}

DateTime DateTime::add_days(int days) const {
    DateTime result = *this;
    result.time_point_ += std::chrono::hours(24 * days);
    return result;
}

DateTime DateTime::add_hours(int hours) const {
    DateTime result = *this;
    result.time_point_ += std::chrono::hours(hours);
    return result;
}

DateTime DateTime::add_minutes(int minutes) const {
    DateTime result = *this;
    result.time_point_ += std::chrono::minutes(minutes);
    return result;
}

bool DateTime::is_before(const DateTime& other) const {
    return time_point_ < other.time_point_;
}

bool DateTime::is_after(const DateTime& other) const {
    return time_point_ > other.time_point_;
}

int DateTime::days_difference(const DateTime& other) const {
    auto duration = time_point_ - other.time_point_;
    return std::chrono::duration_cast<std::chrono::hours>(duration).count() / 24;
}

bool DateTime::operator==(const DateTime& other) const {
    return time_point_ == other.time_point_;
}

bool DateTime::operator!=(const DateTime& other) const {
    return !(*this == other);
}

bool DateTime::operator<(const DateTime& other) const {
    return time_point_ < other.time_point_;
}

bool DateTime::operator<=(const DateTime& other) const {
    return time_point_ <= other.time_point_;
}

bool DateTime::operator>(const DateTime& other) const {
    return time_point_ > other.time_point_;
}

bool DateTime::operator>=(const DateTime& other) const {
    return time_point_ >= other.time_point_;
}

// StringUtils implementation
std::string StringUtils::trim(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\n\r\f\v");
    if (start == std::string::npos) {
        return "";
    }
    
    size_t end = str.find_last_not_of(" \t\n\r\f\v");
    return str.substr(start, end - start + 1);
}

std::string StringUtils::to_lower(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
}

std::string StringUtils::to_upper(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::toupper);
    return result;
}

std::vector<std::string> StringUtils::split(const std::string& str, char delimiter) {
    std::vector<std::string> tokens;
    std::stringstream ss(str);
    std::string token;
    
    while (std::getline(ss, token, delimiter)) {
        tokens.push_back(token);
    }
    
    return tokens;
}

std::string StringUtils::join(const std::vector<std::string>& strings, const std::string& delimiter) {
    if (strings.empty()) {
        return "";
    }
    
    std::ostringstream oss;
    oss << strings[0];
    
    for (size_t i = 1; i < strings.size(); ++i) {
        oss << delimiter << strings[i];
    }
    
    return oss.str();
}

bool StringUtils::starts_with(const std::string& str, const std::string& prefix) {
    return str.length() >= prefix.length() && 
           str.compare(0, prefix.length(), prefix) == 0;
}

bool StringUtils::ends_with(const std::string& str, const std::string& suffix) {
    return str.length() >= suffix.length() && 
           str.compare(str.length() - suffix.length(), suffix.length(), suffix) == 0;
}

std::string StringUtils::replace_all(const std::string& str, const std::string& from, const std::string& to) {
    std::string result = str;
    size_t pos = 0;
    
    while ((pos = result.find(from, pos)) != std::string::npos) {
        result.replace(pos, from.length(), to);
        pos += to.length();
    }
    
    return result;
}

bool StringUtils::is_numeric(const std::string& str) {
    if (str.empty()) {
        return false;
    }
    
    return std::all_of(str.begin(), str.end(), ::isdigit);
}

bool StringUtils::is_valid_email(const std::string& email) {
    const std::regex email_pattern(R"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})");
    return std::regex_match(email, email_pattern);
}

bool StringUtils::is_valid_phone(const std::string& phone) {
    const std::regex phone_pattern(R"(^\+?[1-9]\d{1,14}$)");
    return std::regex_match(phone, phone_pattern);
}

// ValidationUtils implementation
bool ValidationUtils::is_valid_name(const std::string& name) {
    if (name.empty() || name.length() > 100) {
        return false;
    }
    
    // Check if name contains only letters, spaces, hyphens, and apostrophes
    const std::regex name_pattern(R"([a-zA-Z\s\-']+)");
    return std::regex_match(name, name_pattern);
}

bool ValidationUtils::is_valid_age(int age) {
    return age >= 0 && age <= 150;
}

bool ValidationUtils::is_valid_gender(const std::string& gender) {
    std::string lower_gender = StringUtils::to_lower(gender);
    return lower_gender == "male" || lower_gender == "female" || lower_gender == "other";
}

bool ValidationUtils::is_valid_medical_id(const std::string& medical_id) {
    // Medical ID should be alphanumeric and 6-20 characters long
    if (medical_id.length() < 6 || medical_id.length() > 20) {
        return false;
    }
    
    const std::regex id_pattern(R"([a-zA-Z0-9]+)");
    return std::regex_match(medical_id, id_pattern);
}

bool ValidationUtils::is_valid_appointment_time(const DateTime& appointment_time) {
    DateTime now = DateTime::now();
    
    // Appointment must be in the future
    if (!appointment_time.is_after(now)) {
        return false;
    }
    
    // Check if appointment is during working hours (8 AM to 6 PM)
    std::string time_str = appointment_time.to_time_string();
    int hour = std::stoi(time_str.substr(0, 2));
    
    return hour >= 8 && hour < 18;
}

// IdGenerator implementation
std::random_device IdGenerator::rd_;
std::mt19937 IdGenerator::gen_(rd_());
std::uniform_int_distribution<> IdGenerator::dis_(0, 35);

std::string IdGenerator::generate_patient_id() {
    return "P" + generate_random_string(8);
}

std::string IdGenerator::generate_doctor_id() {
    return "D" + generate_random_string(8);
}

std::string IdGenerator::generate_appointment_id() {
    return "A" + generate_random_string(10);
}

std::string IdGenerator::generate_random_string(size_t length) {
    const std::string chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    std::string result;
    result.reserve(length);
    
    for (size_t i = 0; i < length; ++i) {
        result += chars[dis_(gen_)];
    }
    
    return result;
}

} // namespace Core
} // namespace HospitalSystem
